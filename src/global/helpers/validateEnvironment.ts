import { logger } from '../services';

/**
 * SECURITY HARDENING: Environment variable validation
 * Ensures all required security-related environment variables are properly set
 */

export const validateEnvironment = (): void => {

  const requiredVars = [
    'NODE_ENV',
    'NODE_PORT',
    'EXPRESS_SESSION_NAME',
    'EXPRESS_SESSION_SECRET',
    'EXPRESS_SESSION_TIMEOUT',
    'GOOGLE_OAUTH_CLIENT_ID',
    'POSTGRES_HOST',
    'POSTGRES_DATABASE',
    'POSTGRES_USER',
    'POSTGRES_PASSWORD',
    'REDIS_HOST',
    'REDIS_PORT',
    'REDIS_PASSWORD',
    'APP_NAME',
    'APP_WEBSITE_URL',
    'APP_URL_DEV',
    'APP_URL_PROD',
    'APP_EMAIL',
    'APP_DOMAIN',
  ];

  const missingVars: string[] = [];
  const weakVars: string[] = [];

  // Check for missing variables
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      missingVars.push(varName);
    }
  }

  // Check for weak security configurations
  if (process.env.EXPRESS_SESSION_SECRET && process.env.EXPRESS_SESSION_SECRET.length < 32) {
    weakVars.push('EXPRESS_SESSION_SECRET (too short, minimum 32 characters)');
  }

  if (process.env.NODE_ENV === 'production' && !process.env.REDIS_PASSWORD) {
    weakVars.push('REDIS_PASSWORD (required in production)');
  }

  if (process.env.NODE_ENV === 'production' && process.env.APP_URL_PROD?.startsWith('http://')) {
    weakVars.push('APP_URL_PROD (should use HTTPS in production)');
  }

  // Log findings
  if (missingVars.length > 0) {
    logger.error('Missing required environment variables:', { missingVars });
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  if (weakVars.length > 0) {
    logger.warn('Weak security configuration detected:', { weakVars });
    if (process.env.NODE_ENV === 'production') {
      throw new Error(`Weak security configuration in production: ${weakVars.join(', ')}`);
    }
  }

  logger.info('Environment validation passed');
};

/**
 * SECURITY HARDENING: Validate session configuration
 */
export const validateSessionConfig = (): void => {
  const sessionTimeout = parseInt(process.env.EXPRESS_SESSION_TIMEOUT || '0', 10);

  // In production, enforce a stricter session timeout for security.
  // A long session timeout increases the risk of session hijacking.
  if (process.env.NODE_ENV === 'production') {
    const maxProdTimeout = 14 * 24 * 60 * 60 * 1000; // 2 weeks
    if (sessionTimeout > maxProdTimeout) {
      logger.warn(
        'SECURITY: Session timeout exceeds 2 weeks in production.',
        {
          configuredTimeoutMs: sessionTimeout,
          recommendedMaxMs: maxProdTimeout,
        },
      );
    }
  }
};
