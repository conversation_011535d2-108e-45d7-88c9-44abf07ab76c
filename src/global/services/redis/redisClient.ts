import Redis from 'ioredis';
import { Var } from '../../var';

// Redis client instance (singleton)
let redisClient: Redis | null = null;

// Connection options with performance optimizations
const redisOptions = {
  host: Var.redis.host,
  port: Var.redis.port,
  password: Var.redis.password || undefined,
  // Connection optimization
  connectTimeout: 10000,
  maxRetriesPerRequest: 3,
  enableReadyCheck: true,
  // Performance optimizations
  enableOfflineQueue: true,
  // Retry strategy with exponential backoff
  retryStrategy: (times: number) => {
    const delay = Math.min(Math.exp(times) * 100, 5000);
    return delay;
  },
  // Reconnect strategy
  reconnectOnError: (err: Error) => {
    const targetError = 'READONLY';
    if (err.message.includes(targetError)) {
      // Only reconnect when the error contains "READONLY"
      return true;
    }
    return false;
  },
};

/**
 * Initialize Redis client with optimized settings
 * @returns Redis client instance
 */
export const initRedisClient = (): Redis => {
  if (redisClient) {
    return redisClient;
  }

  if (Var.node.env === 'prod') {
    console.log(`${Var.app.emoji.success} Initializing Redis client in production mode...`);
  } else {
    console.log(`${Var.app.emoji.success} Initializing Redis client in development mode...`);
  }

  // Create Redis client with optimized configuration
  redisClient = new Redis(redisOptions);

  // Handle connection events
  redisClient.on('connect', () => {
    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Connected to Redis server`);
    }
  });

  redisClient.on('ready', () => {
    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Redis client ready`);
    }
  });

  redisClient.on('error', err => {
    console.error(`${Var.app.emoji.failure} Redis error:`, err);
  });

  redisClient.on('reconnecting', () => {
    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.warning} Redis reconnecting...`);
    }
  });

  return redisClient;
};

/**
 * Get the Redis client instance (with lazy initialization)
 * @returns Redis client instance
 */
export const getRedisClient = (): Redis => {
  if (!redisClient) {
    return initRedisClient();
  }
  return redisClient;
};

/**
 * Close Redis connection with proper cleanup
 */
export const closeRedisConnection = async (): Promise<void> => {
  if (redisClient) {
    try {
      // Use quit instead of disconnect to ensure all commands are processed
      await redisClient.quit();
      redisClient = null;
      if (Var.node.env === 'dev') {
        console.log(`${Var.app.emoji.success} Redis connection closed gracefully`);
      }
    } catch (error) {
      console.error(`${Var.app.emoji.failure} Error closing Redis connection:`, error);
      // Force disconnect if quit fails
      if (redisClient) {
        redisClient.disconnect();
        redisClient = null;
      }
    }
  }
};
