import { Var } from '../../var';
import { getRedisClient } from './redisClient';

// Cache key prefix for surveys
const SURVEY_CACHE_PREFIX = 'survey:';

/**
 * Generate cache key for a survey
 * @param publicKey - The public key of the survey
 * @returns The cache key
 */
const generateSurveyCacheKey = (publicKey: string): string => {
  return `${SURVEY_CACHE_PREFIX}${publicKey}`;
};

/**
 * Get survey data from Redis cache with optimized performance
 * @param publicKey - The public key of the survey
 * @returns The cached survey data or null if not found
 */
export const getSurveyFromCache = async (publicKey: string): Promise<any | null> => {
  if (!publicKey) {
    return null;
  }

  try {
    const redisClient = getRedisClient();

    // Get data from Redis using the key format survey:{publicKey}
    const cacheKey = generateSurveyCacheKey(publicKey);
    const cachedData = await redisClient.get(cacheKey);

    if (!cachedData) {
      if (Var.node.env === 'dev') {
        console.log(`${Var.app.emoji.warning} Cache miss for survey: ${publicKey}`);
      }
      return null;
    }

    // Parse the cached data with error handling
    try {
      const parsedData = JSON.parse(cachedData);
      if (Var.node.env === 'dev') {
        console.log(`${Var.app.emoji.success} Cache hit for survey: ${publicKey}`);
      }
      return parsedData;
    } catch (parseError) {
      console.error(`${Var.app.emoji.failure} Error parsing cached data:`, parseError);
      // If parsing fails, invalidate the cache entry
      await redisClient.del(cacheKey);
      return null;
    }
  } catch (error) {
    // Only log in development or if it's a critical error
    if (Var.node.env === 'dev' || (error instanceof Error && error.message.includes('ECONNREFUSED'))) {
      console.error(`${Var.app.emoji.failure} Redis error:`, error);
    }
    // Fail gracefully - don't let Redis errors affect the application
    return null;
  }
};
