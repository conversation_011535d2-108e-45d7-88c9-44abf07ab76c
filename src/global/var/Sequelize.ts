import { Sequelize } from 'sequelize';
import { Var } from '.';
import { logger } from '../services/logger';

// SECURITY FIX: Enhanced database configuration with security hardening
export const SequelizeVar = new Sequelize(Var.postgres.database!, Var.postgres.user!, Var.postgres.password!, {
  host: Var.postgres.host,
  dialect: 'postgres',
  logging: Var.node.env === 'dev' ? msg => logger.debug('Sequelize:', msg) : false, // Disable SQL logging in production
  dialectOptions: {
    ssl:
      Var.node.env === 'prod'
        ? {
            require: true,
            rejectUnauthorized: true,
            // Additional SSL security options
            checkServerIdentity: true,
            secureProtocol: 'TLSv1_2_method',
          }
        : false,
    connectTimeout: 60000,
    // SECURITY FIX: Additional security options
    statement_timeout: 30000, // 30 second query timeout
    query_timeout: 30000,
    application_name: 'sensefolks-api', // For connection tracking
  },
  pool: {
    max: 20,
    min: 5,
    acquire: 60000,
    idle: 10000,
    // SECURITY FIX: Connection validation
    validate: () => true,
    evict: 30000, // Remove idle connections after 30 seconds
  },
  retry: {
    max: 3,
    match: [
      /SequelizeConnectionError/,
      /SequelizeConnectionRefusedError/,
      /SequelizeHostNotFoundError/,
      /SequelizeHostNotReachableError/,
      /SequelizeInvalidConnectionError/,
      /SequelizeConnectionTimedOutError/,
      /TimeoutError/,
    ],
    backoffBase: 1000,
    backoffExponent: 1.5,
  },
  benchmark: Var.node.env === 'dev',
  define: {
    timestamps: true,
    paranoid: true, // Soft deletes for audit trail
    underscored: true,
    freezeTableName: true,
    // SECURITY FIX: Additional model security options
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci',
  },
  // SECURITY FIX: Query security options
  quoteIdentifiers: true, // Always quote identifiers
  isolationLevel: 'READ_COMMITTED', // Prevent dirty reads
});

// SECURITY FIX: Enhanced database hooks with security monitoring
SequelizeVar.addHook('beforeConnect', (config: any) => {
  logger.info('Connecting to database...', {
    host: config.host,
    database: config.database,
    user: config.username,
    ssl: !!(config.dialectOptions as any)?.ssl,
  });
});

SequelizeVar.addHook('afterConnect', (connection: any, config: any) => {
  logger.info('Connected to database successfully', {
    host: config.host,
    database: config.database,
    connectionId: (connection as any)?.processID || 'unknown',
  });
});

SequelizeVar.addHook('beforeDisconnect', () => {
  logger.info('Disconnecting from database...');
});

// SECURITY FIX: Query monitoring and validation hooks
SequelizeVar.addHook('beforeQuery', (options: any, model: any) => {
  const startTime = Date.now();
  (options as any).startTime = startTime;

  // Log slow queries and potential security issues
  if (Var.node.env === 'dev') {
    logger.debug('Executing query', {
      sql: (options as any).sql?.substring(0, 200) + ((options as any).sql && (options as any).sql.length > 200 ? '...' : ''),
      model: (model as any)?.name || 'unknown',
    });
  }

  // Check for potentially dangerous queries
  if ((options as any).sql) {
    const sql = (options as any).sql.toLowerCase();
    const dangerousPatterns = [
      /drop\s+table/i,
      /truncate\s+table/i,
      /delete\s+from\s+\w+\s*;?\s*$/i, // DELETE without WHERE
      /update\s+\w+\s+set\s+.*\s*;?\s*$/i, // UPDATE without WHERE
      /grant\s+/i,
      /revoke\s+/i,
      /create\s+user/i,
      /alter\s+user/i,
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(sql)) {
        logger.warn('Potentially dangerous query detected', {
          sql: (options as any).sql.substring(0, 500),
          pattern: pattern.toString(),
          model: (model as any)?.name || 'unknown',
        });
        break;
      }
    }
  }
});

SequelizeVar.addHook('afterQuery', (options: any, model: any) => {
  const duration = Date.now() - ((options as any).startTime || Date.now());

  // Log slow queries (>1 second)
  if (duration > 1000) {
    logger.warn('Slow query detected', {
      duration: `${duration}ms`,
      sql: (options as any).sql?.substring(0, 200) + ((options as any).sql && (options as any).sql.length > 200 ? '...' : ''),
      model: (model as any)?.name || 'unknown',
    });
  }

  // Log in development for performance monitoring
  if (Var.node.env === 'dev' && duration > 100) {
    logger.debug('Query completed', {
      duration: `${duration}ms`,
      model: (model as any)?.name || 'unknown',
    });
  }
});

process.on('SIGINT', async () => {
  try {
    await SequelizeVar.close();
    logger.info('Database connection closed due to app termination');
    process.exit(0);
  } catch (err) {
    logger.error('Error closing database connection', err);
    process.exit(1);
  }
});
