import { Sequelize } from 'sequelize';
import { Var } from '.';

export const SequelizeVar = new Sequelize(Var.postgres.database!, Var.postgres.user!, Var.postgres.password!, {
  host: Var.postgres.host,
  dialect: 'postgres',
  logging: Var.node.env === 'dev' ? console.log : false,

  pool: {
    max: 20,
    min: 5,
    idle: 10000,
    acquire: 30000,
    evict: 1000,
  },

  define: {
    // Enable timestamps by default (created_at and updated_at)
    timestamps: true,
    // Disable automatic pluralization of table names
    freezeTableName: true,
    // Use underscored style for field names (field_name instead of fieldName)
    underscored: true,
  },

  retry: {
    max: 3,
    match: [
      /SequelizeConnectionError/,
      /SequelizeConnectionRefusedError/,
      /SequelizeHostNotFoundError/,
      /SequelizeHostNotReachableError/,
      /SequelizeInvalidConnectionError/,
      /SequelizeConnectionTimedOutError/,
      /TimeoutError/,
    ],
  },

  benchmark: false,
});
