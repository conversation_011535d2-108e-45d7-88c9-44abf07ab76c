export interface VarInterface {
  app: {
    name: string;
    contact: {
      email: string;
    };
    domain: string;
    emoji: {
      success: string;
      failure: string;
      warning: string;
      info: string;
    };
    url: {
      dev: string;
      prod: string;
    };
    website: {
      url: string;
    };
    owner: {
      name: string;
      website: {
        url: string;
      };
      contact: {
        address: string;
        email: string;
      };
    };
    api: {
      version: string;
    };
  };
  redis: {
    host: string;
    port: string;
    password: string;
  };
  postgres: {
    host: string;
    database: string;
    user: string;
    password: string;
  };
  postmark: {
    token: string;
    template: {
      accountChangeNotification: {
        id: number;
      };
      emailVerificationCode: {
        id: number;
      };
      passwordResetCode: {
        id: number;
      };
    };
  };
  node: {
    env: string;
    port: number;
    express: {
      session: {
        secret: string;
        name: string;
        maxAge: string;
      };
    };
    db: {
      reset: boolean;
      alter: boolean;
    };
  };
  google: {
    oauth: {
      clientId: string;
    };
  };
}
