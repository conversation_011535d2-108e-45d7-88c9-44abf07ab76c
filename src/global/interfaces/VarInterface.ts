// Interface for global variables
export interface VarInterface {
  app: {
    emoji: {
      success: string;
      failure: string;
      warning: string;
      info: string;
    };
    api: {
      version: string;
    };
  };
  postgres: {
    host: string;
    database: string;
    user: string;
    password: string;
  };
  redis: {
    host: string;
    port: number;
    password: string | null;
    cacheTTL: number;
  };
  node: {
    env: string;
    port: number;
    db: {
      reset: boolean;
      alter: boolean;
    };
  };
}
