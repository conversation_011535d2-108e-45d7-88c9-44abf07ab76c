import { Request, Response, NextFunction } from 'express';

import { Var } from '../../var';
import { logger } from '../../services/logger';

export const ExtractAccountIdFromRequest = (req: Request, res: Response, next: NextFunction) => {
  const accountId = req.session?.accountId;

  if (!accountId) {
    logger.warn('No accountId found in session');

    // Clear the session cookie immediately
    res.clearCookie(Var.node.express.session.name, {
      path: '/',
      httpOnly: true,
      secure: Var.node.env === 'prod',
      sameSite: 'lax',
      domain: Var.node.env === 'prod' ? Var.app.domain : undefined,
    });

    // Clear the invalid session (async operation, but we don't wait for it)
    if (req.session) {
      req.session.destroy((error: Error) => {
        if (error) {
          logger.error('Error destroying invalid session', { error });
        } else {
          logger.debug('Invalid session destroyed');
        }
      });
    }

    return res.status(200).json({
      success: false,
      message: `${Var.app.emoji.failure} Session invalid. Please log in again.`,
    });
  }

  logger.debug('AccountId found in session', { accountId });
  res.locals.accountId = accountId;
  next();
};
