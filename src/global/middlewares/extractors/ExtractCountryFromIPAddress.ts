import { Request, Response, NextFunction } from 'express';
import geoip from 'geoip-lite';
import { logger } from '../../services/logger';

export const ExtractCountryFromIPAddress = (_req: Request, res: Response, next: NextFunction) => {
  const geoData = geoip.lookup(res.locals.clientIPAddress);
  let originCountry: string;

  if (!geoData) {
    logger.warn('Unable to ascertain origin country, defaulting to IN', {
      clientIP: res.locals.clientIPAddress,
    });
    originCountry = 'IN';
  } else {
    originCountry = geoData.country;
  }

  res.locals.originCountry = originCountry;
  logger.debug('Origin country extracted', {
    country: res.locals.originCountry,
    clientIP: res.locals.clientIPAddress,
  });
  next();
};
