import { Request, Response, NextFunction } from 'express';
import geoip from 'geoip-lite';
import { Var } from '../../var';

// Cache for IP to country lookups to reduce repeated lookups
const countryCache: Map<string, any> = new Map();

/**
 * Middleware to extract the country from the IP address
 * Optimized with caching to improve performance
 */
export const ExtractCountryFromIPAddress = (req: Request, res: Response, next: NextFunction) => {
  const ip = res.locals.clientIPAddress;
  let originCountry: any;

  // Check if we have a cached result for this IP
  if (countryCache.has(ip)) {
    originCountry = countryCache.get(ip);
  } else {
    // Perform the lookup
    originCountry = geoip.lookup(ip);

    // Cache the result for future requests
    if (originCountry) {
      countryCache.set(ip, originCountry);
    }
  }

  // Default to 'IN' if no country found
  if (!originCountry) {
    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.warning} Unable to ascertain origin country. Defaulting to 'IN'`);
    }
    originCountry = 'IN';
  }

  // Store the country in res.locals
  res.locals.originCountry = originCountry;

  // Only log in development mode to reduce overhead in production
  if (Var.node.env === 'dev') {
    console.log(`${Var.app.emoji.success} Origin country: ${typeof originCountry === 'string' ? originCountry : originCountry.country}`);
  }

  next();
};
