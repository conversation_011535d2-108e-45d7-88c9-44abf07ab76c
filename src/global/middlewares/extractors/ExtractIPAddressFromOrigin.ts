import { Request, Response, NextFunction } from 'express';
import { Var } from '../../var';

/**
 * Middleware to extract the IP address from the request
 * Optimized to reduce logging overhead and improve performance
 */
export const ExtractIPAddressFromOrigin = (req: Request, res: Response, next: NextFunction) => {
  // Extract IP address from request headers or connection
  // Use string type instead of any for better type safety
  const ip: string = ((req.headers['x-forwarded-for'] as string) || req.socket.remoteAddress || '').split(',')[0].trim(); // Handle potential comma-separated list

  // Use a default IP in development mode to avoid blocking requests
  if (!ip) {
    if (Var.node.env === 'dev') {
      // Use a default IP in development mode
      res.locals.clientIPAddress = '127.0.0.1';
      console.log(`${Var.app.emoji.warning} No IP found, using default: 127.0.0.1`);
      return next();
    } else {
      // Only log in production for security reasons
      console.log(`${Var.app.emoji.failure} Invalid IP`);
      return res.status(400).json({
        success: false,
        message: 'Invalid IP address',
      });
    }
  }

  // Store the IP address in res.locals
  res.locals.clientIPAddress = ip;

  // Only log in development mode to reduce overhead in production
  if (Var.node.env === 'dev') {
    console.log(`${Var.app.emoji.success} Origin IP: ${res.locals.clientIPAddress}`);
  }

  next();
};
