import { Request, Response, NextFunction } from 'express';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

/**
 * Middleware to block requests based on origin validation
 * Ensures only authorized origins can access the API
 * In production: strict origin checking
 * In development: relaxed origin checking with debug logging
 */
export const BlockRequestByOrigin = async (_req: Request, res: Response, next: NextFunction) => {
  if (Var.node.env === 'prod') {
    // Production environment: strict origin validation
    if (!res.locals.origin) {
      logger.warn('No origin provided in production environment', {
        ip: _req.ip,
        userAgent: _req.headers['user-agent'],
      });
      return res.status(200).json({
        success: false,
        message: `${Var.app.emoji.failure} You are not authorized to make this request`,
      });
    }

    if (res.locals.origin !== Var.app.url.prod) {
      logger.warn('Unauthorized origin attempted access', {
        origin: res.locals.origin,
        expectedOrigin: Var.app.url.prod,
        ip: _req.ip,
        userAgent: _req.headers['user-agent'],
      });
      return res.status(200).json({
        success: false,
        message: `${Var.app.emoji.failure} You are not authorized to make this request`,
      });
    }
  } else {
    // Development environment: relaxed validation with debug logging
    if (!res.locals.origin) {
      logger.debug('Request with no origin allowed in development environment', {
        ip: _req.ip,
        userAgent: _req.headers['user-agent'],
      });
      // DEBUG: Console output in development only
      console.log(`${Var.app.emoji.warning} [DEV] Request with no origin allowed in development environment`);
    }
  }

  // Log successful origin validation
  logger.debug('Origin validation successful', {
    origin: res.locals.origin || 'No origin (development)',
    environment: Var.node.env,
  });

  // DEBUG: Console output in development only
  if (Var.node.env === 'dev') {
    console.log(`${Var.app.emoji.success} [DEV] ${res.locals.origin || 'No origin (development)'} is authorized origin`);
  }

  next();
};
