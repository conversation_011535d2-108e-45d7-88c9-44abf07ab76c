import { Request, Response, NextFunction } from 'express';
import { Var } from '../../../var';

export const BlockLoggedInAccount = (req: Request, res: Response, next: NextFunction) => {
  let isUserLoggedIn = !!req.session!.accountId;

  if (isUserLoggedIn) {
    // DEBUG: Console output in development only
    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.failure} [DEV] ${req.session.accountId} is already logged in`);
    }
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} You are already logged in`,
    });
  }

  // DEBUG: Console output in development only
  if (Var.node.env === 'dev') {
    console.log(`${Var.app.emoji.success} [DEV] User is not logged in`);
  }
  next();
};
