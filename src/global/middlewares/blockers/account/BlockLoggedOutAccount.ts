import { Request, Response, NextFunction } from 'express';

import { Var } from '../../../var';
import { logger } from '../../../services/logger';

export const BlockLoggedOutAccount = (req: Request, res: Response, next: NextFunction) => {
  const isUserLoggedIn = !!req.session!.accountId;

  if (!isUserLoggedIn) {
    logger.debug('User is not logged in');

    // Clear the sessionId cookie when user is logged out
    res.clearCookie(Var.node.express.session.name, {
      path: '/',
      httpOnly: true,
      secure: Var.node.env === 'prod',
      sameSite: 'lax',
      domain: Var.node.env === 'prod' ? Var.app.domain : undefined,
    });

    return res.status(200).json({
      success: false,
      message: `${Var.app.emoji.failure} You are not logged in`,
    });
  }

  logger.debug(`${req.session.accountId} is logged in`);
  next();
};
