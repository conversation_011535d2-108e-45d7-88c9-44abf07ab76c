import { Request, Response, NextFunction } from 'express';
import { readAccountByEmail } from '../../../../components/account/dals';
import { Var } from '../../../var';
import { logger } from '../../../services/logger';

export const BlockNonExistentAccountByEmail = async (_req: Request, res: Response, next: NextFunction) => {
  const account = await readAccountByEmail(res.locals.email);

  if (!account) {
    logger.warn('Account not found', { email: res.locals.email });
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} You are not registered`,
    });
  }

  logger.debug('Account found', { email: res.locals.email });
  next();
};
