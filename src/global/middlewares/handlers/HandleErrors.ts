import { Request, Response, NextFunction, ErrorRequestHandler } from 'express';
import { Var } from '../../var';

/**
 * Global error handler middleware
 * Catches all unhandled errors and returns a standardized error response
 */
export const HandleErrors: ErrorRequestHandler = (err: Error, _req: Request, res: Response, _next: NextFunction) => {
  // Log the error for debugging
  console.error(`${Var.app.emoji.failure} Error: ${err.message}`);
  console.error(err.stack);

  // Send a standardized error response
  res.status(500).json({
    success: false,
    message: 'An internal server error occurred',
    error: Var.node.env === 'dev' ? err.message : undefined,
  });
};
