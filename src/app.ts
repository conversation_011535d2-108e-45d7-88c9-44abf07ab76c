import express, { Request, Response } from 'express';
import cors from 'cors';
import { Var } from './global/var';
import { HandleErrors } from './global/middlewares';
import { getSurveyByPublicKeyRoute } from './components/survey/routes/v1/getSurveyByPublicKeyRoute';

const app = express();

// Allow all origins for the survey API
app.use(cors());

// Parse JSON request bodies
app.use(express.json());

// Trust proxy in production
if (Var.node.env === 'prod') {
  app.set('trust proxy', 1);
}

// Health check endpoint
app.get('/health', (_req: Request, res: Response) => {
  res.status(200).json({
    status: 'ok',
    service: 'request-api',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
  });
});

// Register the survey route
app.use(getSurveyByPublicKeyRoute);

// 404 handler for undefined routes
app.use((req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    path: req.path,
  });
});

// Global error handler
app.use(HandleErrors);

export default app;
