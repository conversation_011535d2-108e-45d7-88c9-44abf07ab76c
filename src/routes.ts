import { Express } from 'express';

// Account routes
import { checkSessionRoute } from './components/account/routes/v1/auth/checkSessionRoute';
import { googleOauthRoute } from './components/account/routes/v1/auth/googleOauthRoute';
import { loginRoute } from './components/account/routes/v1/auth/loginRoute';
import { logoutRoute } from './components/account/routes/v1/auth/logoutRoute';
import { signupRoute } from './components/account/routes/v1/auth/signupRoute';
import { deleteAccountRoute } from './components/account/routes/v1/delete/deleteAccountRoute';
import { getAccountDetailsRoute } from './components/account/routes/v1/details/getAccountDetailsRoute';
import { mailEmailVerificationCodeRoute } from './components/account/routes/v1/mail/mailEmailVerificationCodeRoute';
import { mailPasswordResetCodeRoute } from './components/account/routes/v1/mail/mailPasswordResetCodeRoute';
import { updateAccountRoute } from './components/account/routes/v1/update/updateAccountRoute';
import { updatePasswordRoute } from './components/account/routes/v1/update/updatePasswordRoute';
import { verifyEmailRoute } from './components/account/routes/v1/verify/verifyEmailRoute';

// Survey routes
import {
  createSurveyRoute,
  deleteSurveyRoute,
  getAllSurveysRoute,
  getEmbedCodeRoute,
  getShareUrlRoute,
  getSurveyByIdRoute,
  getSurveyByPublicKeyRoute,
  updateSurveyRoute,
} from './components/survey/routes/v1';

// Response routes
import { discardResponseRoute, exportResponsesRoute, getResponsesAndAnalyticsRoute } from './components/response/routes/v1';

// Security routes
import { cspReportRoute } from './components/security/routes/v1/cspReportRoute';
import { securityMonitoringRoute } from './components/security/routes/v1/securityMonitoringRoute';

// Waitlist routes
import { waitlistRoute } from './components/waitlist/routes/v1';

export const registerAllRoutes = (app: Express): void => {
  // Account routes
  app.use(loginRoute);
  app.use(logoutRoute);
  app.use(signupRoute);
  app.use(googleOauthRoute);
  app.use(checkSessionRoute);
  app.use(deleteAccountRoute);
  app.use(getAccountDetailsRoute);
  app.use(mailEmailVerificationCodeRoute);
  app.use(mailPasswordResetCodeRoute);
  app.use(updateAccountRoute);
  app.use(updatePasswordRoute);
  app.use(verifyEmailRoute);

  // Survey routes
  app.use(createSurveyRoute);
  app.use(getAllSurveysRoute);
  app.use(getSurveyByIdRoute);
  app.use(deleteSurveyRoute);
  app.use(updateSurveyRoute);
  app.use(getShareUrlRoute);
  app.use(getEmbedCodeRoute);
  app.use(getSurveyByPublicKeyRoute);

  // Response routes
  app.use(discardResponseRoute);
  app.use(exportResponsesRoute);
  app.use(getResponsesAndAnalyticsRoute);

  // Security routes
  app.use(cspReportRoute);
  app.use(securityMonitoringRoute);

  // Waitlist routes
  app.use(waitlistRoute);
};
