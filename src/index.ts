import app from './app';
import http from 'http';
import { Sequelize } from './global/var';
import { Var } from './global/var';
import { initRedisClient, closeRedisConnection } from './global/services';
import dotenv from 'dotenv';

dotenv.config();

const startServer = async () => {
  let server: http.Server | null = null;

  try {
    console.log(`${Var.app.emoji.success} Connecting to database...`);
    await Sequelize.authenticate();
    console.log(`${Var.app.emoji.success} Database connection established successfully`);

    console.log(`${Var.app.emoji.success} Loading models...`);
    await import('./components/survey/models/survey/surveyModel');

    // Sync models with database
    console.log(`${Var.app.emoji.success} Syncing models with database...`);
    try {
      // Only reset the database if explicitly requested
      if (Var.node.env === 'dev' && Var.node.db && Var.node.db.reset) {
        console.log(`${Var.app.emoji.warning} Forcing database reset as requested by RESET_DB=true...`);
        await Sequelize.sync({ force: true });
        console.log(`${Var.app.emoji.success} Database reset and tables created`);
      }
      // Only alter tables if explicitly requested
      else if (Var.node.env === 'dev' && Var.node.db && Var.node.db.alter) {
        console.log(`${Var.app.emoji.warning} Altering database tables as requested by ALTER_DB=true...`);
        await Sequelize.sync({ alter: true });
        console.log(`${Var.app.emoji.success} Database tables altered`);
      }
      // Default behavior: just sync without dropping or altering
      else {
        console.log(`${Var.app.emoji.info} Syncing database models without dropping or altering tables...`);
        await Sequelize.sync({ force: false, alter: false });
        console.log(`${Var.app.emoji.success} Database tables synced (tables created if they didn't exist)`);
      }
    } catch (syncError) {
      console.error(`${Var.app.emoji.failure} Failed to sync models with database: ${(syncError as Error).message}`);
      process.exit(1);
    }

    // Initialize Redis client
    console.log(`${Var.app.emoji.success} Initializing Redis client...`);
    initRedisClient();

    // Create and start HTTP server
    server = http.createServer(app);

    server.listen(Var.node.port, () => {
      console.log(`${Var.app.emoji.success} Survey Request API is running on port: ${Var.node.port}`);
      console.log(`${Var.app.emoji.success} Environment: ${Var.node.env}`);
      console.log(`${Var.app.emoji.success} Health check available at: http://localhost:${Var.node.port}/health`);
    });

    // Handle server errors
    server.on('error', error => {
      console.error(`${Var.app.emoji.failure} Server error:`, error);
      process.exit(1);
    });

    // Setup graceful shutdown
    setupGracefulShutdown(server);
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Failed to start server:`, error);
    process.exit(1);
  }
};

/**
 * Setup graceful shutdown handlers
 */
const setupGracefulShutdown = (server: http.Server) => {
  // Handle process termination signals
  const shutdown = async (signal: string) => {
    console.log(`\n${Var.app.emoji.warning} Received ${signal}, shutting down gracefully...`);

    // Close HTTP server
    server.close(() => {
      console.log(`${Var.app.emoji.success} HTTP server closed`);

      // Close database connection
      Sequelize.close()
        .then(async () => {
          console.log(`${Var.app.emoji.success} Database connection closed`);

          // Close Redis connection
          try {
            await closeRedisConnection();
            console.log(`${Var.app.emoji.success} Redis connection closed`);
          } catch (err) {
            console.error(`${Var.app.emoji.failure} Error closing Redis connection:`, err);
          }

          console.log(`${Var.app.emoji.success} Shutdown complete`);
          process.exit(0);
        })
        .catch(err => {
          console.error(`${Var.app.emoji.failure} Error closing database connection:`, err);
          process.exit(1);
        });
    });

    // Force exit after 10 seconds if graceful shutdown fails
    setTimeout(() => {
      console.error(`${Var.app.emoji.failure} Shutdown timed out, forcing exit`);
      process.exit(1);
    }, 10000);
  };

  // Register signal handlers
  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGINT', () => shutdown('SIGINT'));

  // Handle uncaught exceptions
  process.on('uncaughtException', error => {
    console.error(`${Var.app.emoji.failure} Uncaught exception:`, error);
    shutdown('uncaughtException');
  });

  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, _promise) => {
    console.error(`${Var.app.emoji.failure} Unhandled promise rejection:`, reason);
    shutdown('unhandledRejection');
  });
};

// Start the server
startServer();
