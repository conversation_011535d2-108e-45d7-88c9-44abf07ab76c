import { Request, Response } from 'express';
import { logger, redisClient } from '../../../../global/services';

/**
 * SECURITY FIX: Security monitoring dashboard endpoint
 * Provides real-time security metrics and alerts for administrators
 */

export const getSecurityMetricsController = async (req: Request, res: Response) => {
  try {
    // Verify admin access (this would typically check admin role)
    const accountId = res.locals.accountId;
    if (!accountId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
      });
    }

    // Get time range from query parameters
    const timeRange = (req.query.timeRange as string) || '24h';
    const startDate = getStartDate(timeRange);
    const endDate = new Date();

    // Gather security metrics
    const metrics = await gatherSecurityMetrics();

    logger.info('Security metrics requested', {
      accountId: accountId.substring(0, 8) + '...',
      timeRange,
      ip: req.ip,
    });

    return res.status(200).json({
      success: true,
      data: {
        timeRange,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        metrics,
      },
    });
  } catch (error) {
    logger.error('Error retrieving security metrics', {
      error: error instanceof Error ? error.message : String(error),
      accountId: res.locals.accountId,
      ip: req.ip,
    });

    return res.status(500).json({
      success: false,
      message: 'Error retrieving security metrics',
    });
  }
};

export const getSecurityAlertsController = async (req: Request, res: Response) => {
  try {
    const accountId = res.locals.accountId;
    if (!accountId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
      });
    }

    const limit = parseInt(req.query.limit as string) || 50;
    const severity = req.query.severity as string;

    // Get recent security alerts
    const alerts = await getRecentSecurityAlerts(limit, severity);

    logger.info('Security alerts requested', {
      accountId: accountId.substring(0, 8) + '...',
      limit,
      severity,
      ip: req.ip,
    });

    return res.status(200).json({
      success: true,
      data: {
        alerts,
        total: alerts.length,
      },
    });
  } catch (error) {
    logger.error('Error retrieving security alerts', {
      error: error instanceof Error ? error.message : String(error),
      accountId: res.locals.accountId,
      ip: req.ip,
    });

    return res.status(500).json({
      success: false,
      message: 'Error retrieving security alerts',
    });
  }
};

/**
 * Get start date based on time range
 */
function getStartDate(timeRange: string): Date {
  const now = new Date();

  switch (timeRange) {
    case '1h':
      return new Date(now.getTime() - 60 * 60 * 1000);
    case '6h':
      return new Date(now.getTime() - 6 * 60 * 60 * 1000);
    case '24h':
      return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    case '7d':
      return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    case '30d':
      return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    default:
      return new Date(now.getTime() - 24 * 60 * 60 * 1000);
  }
}

/**
 * Gather comprehensive security metrics
 */
async function gatherSecurityMetrics() {
  try {
    const metrics = {
      authentication: await getAuthenticationMetrics(),
      rateLimiting: await getRateLimitingMetrics(),
      securityViolations: await getSecurityViolationMetrics(),
      systemHealth: await getSystemHealthMetrics(),
      threats: await getThreatMetrics(),
    };

    return metrics;
  } catch (error) {
    logger.error('Error gathering security metrics', error);
    return {
      authentication: {},
      rateLimiting: {},
      securityViolations: {},
      systemHealth: {},
      threats: {},
    };
  }
}

/**
 * Get authentication-related metrics
 */
async function getAuthenticationMetrics() {
  try {
    const keys = await redisClient.keys('login-attempts:*');
    const blockedIPs = await redisClient.keys('blocked-ip:*');

    let totalFailedAttempts = 0;
    for (const key of keys) {
      const attempts = await redisClient.get(key);
      if (attempts) {
        totalFailedAttempts += parseInt(attempts, 10);
      }
    }

    return {
      failedLoginAttempts: totalFailedAttempts,
      uniqueFailedIPs: keys.length,
      blockedIPs: blockedIPs.length,
      activeLoginSessions: await redisClient.keys('sess:*').then(keys => keys.length),
    };
  } catch (error) {
    logger.error('Error getting authentication metrics', error);
    return {};
  }
}

/**
 * Get rate limiting metrics
 */
async function getRateLimitingMetrics() {
  try {
    const apiLimitKeys = await redisClient.keys('rate-limit:api:*');
    const authLimitKeys = await redisClient.keys('rate-limit:auth:*');
    const sensitiveOpKeys = await redisClient.keys('rate-limit:sensitive:*');

    return {
      apiRateLimitHits: apiLimitKeys.length,
      authRateLimitHits: authLimitKeys.length,
      sensitiveOpLimitHits: sensitiveOpKeys.length,
      totalRateLimitHits: apiLimitKeys.length + authLimitKeys.length + sensitiveOpKeys.length,
    };
  } catch (error) {
    logger.error('Error getting rate limiting metrics', error);
    return {};
  }
}

/**
 * Get security violation metrics
 */
async function getSecurityViolationMetrics() {
  try {
    const cspViolations = await redisClient.keys('audit:security_violation:*');
    const securityAlerts = await redisClient.keys('security-alerts:*');

    return {
      cspViolations: cspViolations.length,
      securityAlerts: securityAlerts.length,
      totalViolations: cspViolations.length + securityAlerts.length,
    };
  } catch (error) {
    logger.error('Error getting security violation metrics', error);
    return {};
  }
}

/**
 * Get system health metrics
 */
async function getSystemHealthMetrics() {
  try {
    const redisInfo = await redisClient.info('memory');
    const redisMemoryUsage = redisInfo.match(/used_memory:(\d+)/)?.[1] || '0';

    return {
      redisConnected: redisClient.status === 'ready',
      redisMemoryUsage: parseInt(redisMemoryUsage, 10),
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      nodeVersion: process.version,
    };
  } catch (error) {
    logger.error('Error getting system health metrics', error);
    return {};
  }
}

/**
 * Get threat detection metrics
 */
async function getThreatMetrics() {
  try {
    const suspiciousIPs = await redisClient.keys('suspicious-ip:*');
    const maliciousRequests = await redisClient.keys('malicious-request:*');

    return {
      suspiciousIPs: suspiciousIPs.length,
      maliciousRequests: maliciousRequests.length,
      threatLevel: calculateThreatLevel(suspiciousIPs.length, maliciousRequests.length),
    };
  } catch (error) {
    logger.error('Error getting threat metrics', error);
    return {};
  }
}

/**
 * Calculate overall threat level
 */
function calculateThreatLevel(suspiciousIPs: number, maliciousRequests: number): string {
  const totalThreats = suspiciousIPs + maliciousRequests;

  if (totalThreats === 0) return 'low';
  if (totalThreats < 10) return 'medium';
  if (totalThreats < 50) return 'high';
  return 'critical';
}

/**
 * Get recent security alerts
 */
async function getRecentSecurityAlerts(limit: number, severity?: string) {
  try {
    const alertKeys = await redisClient.keys('security-alerts:*');
    const alerts = [];

    // Sort by timestamp (newest first)
    alertKeys.sort((a, b) => {
      const timestampA = parseInt(a.split(':')[1], 10);
      const timestampB = parseInt(b.split(':')[1], 10);
      return timestampB - timestampA;
    });

    for (const key of alertKeys.slice(0, limit)) {
      const alertData = await redisClient.get(key);
      if (alertData) {
        const alert = JSON.parse(alertData);

        // Filter by severity if specified
        if (!severity || alert.severity === severity) {
          alerts.push({
            id: key.split(':')[2],
            timestamp: new Date(parseInt(key.split(':')[1], 10)),
            ...alert,
          });
        }
      }
    }

    return alerts;
  } catch (error) {
    logger.error('Error getting recent security alerts', error);
    return [];
  }
}
