import { logger } from '../../../../global/services';

// Import DAL functions for ownership verification
import { readSurveyById } from '../../../survey/dals';
import { checkResponseOwnership } from '../../../response/dals';

/**
 * SECURITY FIX: Centralized authorization helper to prevent IDOR vulnerabilities
 * Verifies resource ownership at the controller level as a secondary check
 */
export const verifyResourceOwnership = async (resourceType: 'survey' | 'response', resourceId: string, accountId: string): Promise<boolean> => {
  try {
    logger.debug('Verifying resource ownership', {
      resourceType,
      resourceId: resourceId.substring(0, 8) + '...',
      accountId: accountId.substring(0, 8) + '...',
    });

    switch (resourceType) {
      case 'survey': {
        const survey = await readSurveyById(resourceId);
        if (!survey) {
          logger.warn('Survey not found during ownership verification', {
            surveyId: resourceId.substring(0, 8) + '...',
            accountId: accountId.substring(0, 8) + '...',
          });
          return false;
        }
        return survey.dataValues.account_id === accountId;
      }

      case 'response': {
        const isOwner = await checkResponseOwnership(resourceId, accountId);
        return isOwner;
      }

      default:
        logger.error('Unknown resource type for ownership verification', { resourceType });
        return false;
    }
  } catch (error) {
    logger.error('Error verifying resource ownership:', {
      error: error instanceof Error ? error.message : String(error),
      resourceType,
      resourceId: resourceId.substring(0, 8) + '...',
      accountId: accountId.substring(0, 8) + '...',
    });
    return false;
  }
};
