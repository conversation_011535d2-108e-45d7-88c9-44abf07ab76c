import crypto from 'crypto';
import { logger } from '../../../../global/services';

/**
 * SECURITY IMPLEMENTATION: 6-digit code verification with timing-safe comparison
 * Verifies 6-digit codes with integrity checking and prevents timing attacks
 * Uses simple hash verification for compatibility with generateSimple6DigitCode
 */
export const verify6DigitCode = (plainCode: string, hashedCode: string): boolean => {
  try {
    // Validate input format
    if (!plainCode || !hashedCode || plainCode.length !== 6) {
      logger.warn('Invalid 6-digit code format', {
        plainCodeLength: plainCode?.length,
        hasHashedCode: !!hashedCode,
      });
      return false;
    }

    // Validate code is numeric
    if (!/^\d{6}$/.test(plainCode)) {
      logger.warn('6-digit code contains non-numeric characters', {
        plainCode: plainCode.substring(0, 3) + '***',
      });
      return false;
    }

    // Generate hash of the plain code and compare with stored hash
    const codeHash = crypto.createHash('sha256').update(plainCode).digest('hex');
    const storedBuffer = Buffer.from(hashedCode, 'hex');
    const codeBuffer = Buffer.from(codeHash, 'hex');

    if (codeBuffer.length !== storedBuffer.length) {
      return false;
    }

    // Timing-safe comparison
    const isValid = crypto.timingSafeEqual(codeBuffer, storedBuffer);

    if (!isValid) {
      logger.warn('6-digit code verification failed', {
        plainCode: plainCode.substring(0, 3) + '***',
      });
    }

    return isValid;
  } catch (error) {
    logger.error('6-digit code verification error:', error);
    return false;
  }
};
