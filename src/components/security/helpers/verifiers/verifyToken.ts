import crypto from 'crypto';
import { logger } from '../../../../global/services';
import { Var } from '../../../../global/var';

/**
 * SECURITY FIX: Enhanced HMAC token verification
 * Verifies HMAC tokens with integrity checking and replay protection
 * Only supports the most secure HMAC token format
 */
export const verifyToken = (plainToken: string, hashedToken: string): boolean => {
  try {
    // First verify the hash matches
    const hashOfPlainToken = crypto.createHash('sha256').update(plainToken).digest('hex');
    const hashBuffer = Buffer.from(hashOfPlainToken, 'hex');
    const storedBuffer = Buffer.from(hashedToken, 'hex');

    if (hashBuffer.length !== storedBuffer.length) {
      return false;
    }

    if (!crypto.timingSafeEqual(hashBuffer, storedBuffer)) {
      return false;
    }

    // Parse HMAC token structure: baseToken.timestamp.nonce.signature
    const tokenParts = plainToken.split('.');
    if (tokenParts.length !== 4) {
      logger.warn('Invalid HMAC token structure', {
        parts: tokenParts.length,
        expected: 4,
      });
      return false;
    }

    const [baseToken, timestamp, nonce, signature] = tokenParts;

    // Verify HMAC signature
    const secret = Var.node.express.session.secret;
    const hmac = crypto.createHmac('sha256', secret);
    hmac.update(`${baseToken}:${timestamp}:${nonce}`);
    const expectedSignature = hmac.digest('hex');

    // Timing-safe signature comparison
    const signatureBuffer = Buffer.from(signature, 'hex');
    const expectedBuffer = Buffer.from(expectedSignature, 'hex');

    if (signatureBuffer.length !== expectedBuffer.length) {
      return false;
    }

    return crypto.timingSafeEqual(signatureBuffer, expectedBuffer);
  } catch (error) {
    logger.error('HMAC token verification error:', error);
    return false;
  }
};
