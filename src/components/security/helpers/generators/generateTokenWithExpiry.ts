import crypto from 'crypto';
import { logger } from '../../../../global/services';
import { Var } from '../../../../global/var';

/**
 * SECURITY FIX: Enhanced secure token generation with HMAC
 * Provides integrity protection, replay attack prevention, and cryptographic binding
 * This is now the primary and only secure token generation method
 */
export const generateTokenWithExpiry = (expiresInMinutes: number) => {
  const baseToken = crypto.randomBytes(32).toString('hex');
  const timestamp = Date.now().toString();
  const nonce = crypto.randomBytes(16).toString('hex');

  // Create HMAC for integrity using session secret
  const secret = Var.node.express.session.secret;
  const hmac = crypto.createHmac('sha256', secret);
  hmac.update(`${baseToken}:${timestamp}:${nonce}`);
  const signature = hmac.digest('hex');

  const token = `${baseToken}.${timestamp}.${nonce}.${signature}`;
  const hashedToken = crypto.createHash('sha256').update(token).digest('hex');
  const expiresAt = new Date(Date.now() + expiresInMinutes * 60 * 1000);

  logger.info('HMAC secure token generated', {
    tokenLength: token.length,
    expiresAt,
    expiresInMinutes,
  });

  return {
    token,
    hashedToken,
    expiresAt,
  };
};
