import { Request, Response, NextFunction } from 'express';

/**
 * SECURITY HARDENING: Additional security headers not covered by Helmet
 * These provide extra protection against various attack vectors
 */
export const AdditionalSecurityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // Prevent MIME type sniffing
  res.setHeader('X-Content-Type-Options', 'nosniff');
  
  // Prevent clickjacking (additional to helmet's frameguard)
  res.setHeader('X-Frame-Options', 'DENY');
  
  // Enable XSS filtering (additional to helmet's xssFilter)
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // Prevent information disclosure
  res.setHeader('X-Powered-By', ''); // Remove server information
  res.setHeader('Server', ''); // Remove server information
  
  // Cache control for sensitive pages
  if (req.path.includes('/auth/') || req.path.includes('/account/')) {
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
  }
  
  // Prevent DNS prefetching for external domains
  res.setHeader('X-DNS-Prefetch-Control', 'off');
  
  // Feature policy restrictions
  res.setHeader('Permissions-Policy', 
    'geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=()'
  );
  
  // Prevent download of executable files
  if (req.path.includes('/export') || req.path.includes('/download')) {
    res.setHeader('Content-Disposition', 'attachment');
    res.setHeader('X-Download-Options', 'noopen');
  }
  
  next();
};
