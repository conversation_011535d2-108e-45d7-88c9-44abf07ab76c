import { Request, Response } from 'express';

import { writeNewAccount, writeEmailVerificationCode } from '../../dals';
import { hashPassword, mailEmailVerificationCode, login } from '../../helpers';
import { generate6DigitCode } from '../../../security/helpers';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';
import { GenerateCsrfToken } from '../../../security/middlewares/CsrfUtils';

export const signupController = async (req: Request, res: Response) => {
  const { name, email, password } = res.locals;
  const hashedPassword = await hashPassword(password);

  const newAccount = await writeNewAccount(name, email, hashedPassword);
  if (!newAccount.success) {
    return res.status(400).json({
      success: false,
      message: newAccount.message,
    });
  }

  const accountData = newAccount.payload as any;
  login(req, res, accountData.id);

  const { code, hashedCode, expiresAt } = generate6DigitCode(24 * 60); // 24 hours
  const verificationResult = await writeEmailVerificationCode(accountData.email, hashedCode, expiresAt);

  if (!verificationResult.success) {
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} Failed to generate verification code. Please contact ${Var.app.contact.email}`,
    });
  }

  const mailResult = await mailEmailVerificationCode(accountData.name.split(' ')[0], accountData.email, code);
  if (!mailResult.success) {
    return res.status(400).json({
      success: false,
      message: mailResult.message,
    });
  }

  logger.info('Email verification code sent successfully', { email });

  // Regenerate the CSRF token after login and send it in the response header.
  const csrfToken = GenerateCsrfToken(req, res);
  res.setHeader('X-CSRF-Token', csrfToken);

  const responseData = {
    name: accountData.name,
    email: accountData.email,
    isEmailVerified: accountData.isEmailVerified,
    isSessionActive: true,
  };

  return res.status(200).json({
    success: true,
    message: `${Var.app.emoji.success} User signed up`,
    payload: responseData,
  });
};
