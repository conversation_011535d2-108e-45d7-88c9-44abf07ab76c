import { Request, Response } from 'express';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

export const checkSessionController = (req: Request, res: Response) => {
  // DEBUG: Log session check details
  logger.debug('Session check performed', {
    sessionId: req.session?.id ? req.session.id.substring(0, 5) + '...' : 'none',
    accountId: req.session?.accountId ? req.session.accountId.substring(0, 5) + '...' : 'none',
    ip: req.ip,
    userAgent: req.headers['user-agent'],
  });

  // DEBUG: Console output in development only
  if (Var.node.env === 'dev') {
    const sessionStatus = req.session?.accountId ? 'active' : 'inactive';
    console.log(`[DEV] Session check: ${sessionStatus} (${req.session?.accountId ? req.session.accountId.substring(0, 8) + '...' : 'no account'})`);
  }

  return res.status(200).json({
    success: true,
    message: `${Var.app.emoji.success} Session active`,
    payload: {
      isSessionActive: true,
    },
  });
};
