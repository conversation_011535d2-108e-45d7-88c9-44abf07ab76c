import { Request, Response, NextFunction } from 'express';
import { logout } from '../../helpers';
import { logger } from '../../../../global/services';

export const logoutController = (req: Request, res: Response, next: NextFunction) => {
  try {
    logger.info('Logout attempt', {
      accountId: req.session?.accountId,
      ip: req.ip,
    });

    logout(req, res, next);
  } catch (error) {
    logger.error('Error in logout controller', { error });
    next(error);
  }
};
