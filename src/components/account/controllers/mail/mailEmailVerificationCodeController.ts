import { Request, Response, NextFunction } from 'express';
import { writeEmailVerificationCode, readAccountByEmail } from '../../dals';
import { mailEmailVerificationCode } from '../../helpers';
import { generate6DigitCode } from '../../../security/helpers';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';
import { AppError } from '../../../../global/middlewares/handlers/HandleErrors';

export const mailEmailVerificationCodeController = async (_req: Request, res: Response, next: NextFunction) => {
  try {
    const { email } = res.locals;

    logger.info(`Email verification code requested`, { email });

    const account = await readAccountByEmail(email);
    if (!account) {
      logger.warn(`Email verification code requested for non-existent account`, { email });
      return res.status(200).json({
        success: true,
        message: `${Var.app.emoji.success} If you have an account with us, you will receive the verification code in the registered email`,
      });
    }

    const { code, hashedCode, expiresAt } = generate6DigitCode(24 * 60); // 24 hours

    const verificationResult = await writeEmailVerificationCode(account.dataValues.email, hashedCode, expiresAt);

    if (!verificationResult.success) {
      logger.error(`Failed to store email verification code`, {
        email,
        error: verificationResult.payload,
      });
      return next(new AppError('Failed to generate verification code', 500));
    }

    const emailResult = await mailEmailVerificationCode(account.dataValues.name.split(' ')[0], account.dataValues.email, code);

    if (!emailResult.success) {
      logger.error(`Failed to send email verification code`, {
        email,
        error: emailResult.payload,
      });
      return next(new AppError('Failed to send verification email', 500));
    }

    logger.info(`Email verification code sent successfully`, { email });

    return res.status(200).json({
      success: true,
      message: `${Var.app.emoji.success} Email verification code sent`,
      payload: {
        email: account.dataValues.email,
        expiresIn: '24 hours',
      },
    });
  } catch (error) {
    logger.error(`Unexpected error sending email verification code`, {
      email: res.locals.email,
      error,
    });
    return next(new AppError('An error occurred while sending verification code', 500));
  }
};
