import { Request, Response, NextFunction } from 'express';
import { writePasswordResetCode, readAccountByEmail } from '../../dals';
import { mailPasswordResetCode } from '../../helpers';
import { generate6DigitCode } from '../../../security/helpers';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';
import { AppError } from '../../../../global/middlewares/handlers/HandleErrors';

export const mailPasswordResetCodeController = async (_req: Request, res: Response, next: NextFunction) => {
  try {
    const { email } = res.locals;

    logger.info(`Password reset requested`, { email });

    const account = await readAccountByEmail(email);
    if (!account) {
      logger.warn(`Password reset requested for non-existent account`, { email });
      return res.status(200).json({
        success: true,
        message: `${Var.app.emoji.success} If you have an account with us, you will receive the password reset instructions in the registered email`,
      });
    }

    if (!account.dataValues.is_email_verified) {
      logger.warn(`Password reset requested for unverified account`, { email });
      return res.status(200).json({
        success: true,
        message: `${Var.app.emoji.success} If you have a verified account with us, you will receive the password reset instructions in the registered email`,
      });
    }

    const { code, hashedCode, expiresAt } = generate6DigitCode(15); // 15 minutes

    const resetResult = await writePasswordResetCode(account.dataValues.email, hashedCode, expiresAt);

    if (!resetResult.success) {
      logger.error(`Failed to store password reset token`, {
        email,
        error: resetResult.payload,
      });
      return next(new AppError('Failed to generate reset token', 500));
    }

    const emailResult = await mailPasswordResetCode(account.dataValues.name.split(' ')[0], account.dataValues.email, code);

    if (!emailResult.success) {
      logger.error(`Failed to send password reset email`, {
        email,
        error: emailResult.payload,
      });
      return next(new AppError('Failed to send reset email', 500));
    }

    logger.info(`Password reset email sent successfully`, { email });

    // Return success response
    return res.status(200).json({
      success: true,
      message: `${Var.app.emoji.success} Password reset instructions sent`,
      payload: {
        email: account.dataValues.email,
        expiresIn: '15 minutes',
      },
    });
  } catch (error) {
    logger.error(`Unexpected error sending password reset email`, {
      email: res.locals.email,
      error,
    });
    return next(new AppError('An error occurred while sending reset instructions', 500));
  }
};
