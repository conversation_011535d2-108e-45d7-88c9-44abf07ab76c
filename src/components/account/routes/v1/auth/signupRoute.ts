import { Router } from 'express';
import {
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedInAccount,
  BlockExistingAccountByEmail,
} from '../../../../../global/middlewares';
import { AccountCreationLimiter } from '../../../../security';
import { validateSignupPayload, formatSignupPayload } from '../../../middlewares';
import { GenerateApiVersionPath } from '../../../../../global/helpers';
import { signupController } from '../../../controllers';

export const signupRoute = Router();

signupRoute.post(
  `${GenerateApiVersionPath()}auth/signup`,
  // Apply rate limiting first
  AccountCreationLimiter,
  // Extract request information
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  // Block already logged in users
  BlockLoggedInAccount,
  // Validate and format the request
  validateSignupPayload,
  formatSignupPayload,
  // Check if account already exists
  BlockExistingAccountByEmail,
  // Process the request
  signupController,
);
