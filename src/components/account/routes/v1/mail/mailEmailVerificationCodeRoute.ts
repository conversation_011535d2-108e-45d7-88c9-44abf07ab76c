import { Router } from 'express';
import { ExtractOriginFromRequest, BlockRequestByOrigin, ExtractIPAddressFromOrigin, ExtractCountryFromIPAddress } from '../../../../../global/middlewares';
import { EmailVerificationLimiter } from '../../../../security';
import { validateMailPayload, formatMailPayload } from '../../../middlewares';
import { GenerateApiVersionPath } from '../../../../../global/helpers';
import { mailEmailVerificationCodeController } from '../../../controllers';

export const mailEmailVerificationCodeRoute = Router();

mailEmailVerificationCodeRoute.post(
  `${GenerateApiVersionPath()}account/email/send-code`,
  // Apply rate limiting first
  EmailVerificationLimiter,
  // Extract request information
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  // Validate and format the request
  validateMailPayload,
  formatMailPayload,
  // Process the request
  mailEmailVerificationCodeController,
);
