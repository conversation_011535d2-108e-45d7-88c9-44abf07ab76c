import { Router } from 'express';
import { ExtractOriginFromRequest, BlockRequestByOrigin, ExtractIPAddressFromOrigin, ExtractCountryFromIPAddress } from '../../../../../global/middlewares';
import { PasswordResetLimiter } from '../../../../security';
import { validateMailPayload, formatMailPayload } from '../../../middlewares';
import { GenerateApiVersionPath } from '../../../../../global/helpers';
import { mailPasswordResetCodeController } from '../../../controllers';

export const mailPasswordResetCodeRoute = Router();

mailPasswordResetCodeRoute.post(
  `${GenerateApiVersionPath()}account/password/send-code`,
  // Apply rate limiting first
  PasswordResetLimiter,
  // Extract request information
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  // Validate and format the request
  validateMailPayload,
  formatMailPayload,
  // Process the request
  mailPasswordResetCodeController,
);
