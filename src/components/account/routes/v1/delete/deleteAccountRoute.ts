import { Router } from 'express';
import {
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
} from '../../../../../global/middlewares';
import { GenerateApiVersionPath } from '../../../../../global/helpers';
import { deleteAccountController } from '../../../controllers';

export const deleteAccountRoute = Router();

deleteAccountRoute.delete(
  `${GenerateApiVersionPath()}account`,
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  deleteAccountController,
);
