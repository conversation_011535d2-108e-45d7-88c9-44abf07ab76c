import { Router } from 'express';
import {
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
} from '../../../../../global/middlewares';
import { GenerateApiVersionPath } from '../../../../../global/helpers';
import { getAccountDetailsController } from '../../../controllers';

export const getAccountDetailsRoute = Router();

getAccountDetailsRoute.get(
  `${GenerateApiVersionPath()}account`,
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  getAccountDetailsController,
);
