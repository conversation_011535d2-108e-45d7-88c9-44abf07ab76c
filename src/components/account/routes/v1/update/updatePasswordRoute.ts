import { Router } from 'express';
import { ExtractOriginFromRequest, BlockRequestByOrigin, ExtractIPAddressFromOrigin, ExtractCountryFromIPAddress } from '../../../../../global/middlewares';
import { ApiLimiter } from '../../../../security';
import { validatePasswordResetPayload, formatPasswordResetPayload } from '../../../middlewares';
import { GenerateApiVersionPath } from '../../../../../global/helpers';
import { updatePasswordController } from '../../../controllers';

export const updatePasswordRoute = Router();

updatePasswordRoute.put(
  `${GenerateApiVersionPath()}account/password`,
  // Apply rate limiting first
  ApiLimiter,
  // Extract request information
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  // Validate and format the request
  validatePasswordResetPayload,
  formatPasswordResetPayload,
  // Process the request
  updatePasswordController,
);
