import Jo<PERSON> from 'joi';
import { Request, Response, NextFunction } from 'express';
import { Var } from '../../../../global/var';

const emailVerificationPayloadSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .min(5)
    .max(128)
    .lowercase()
    .trim()
    .required(),
  emailVerificationCode: Joi.string().trim().length(6).required(),
});

export const validateEmailVerificationPayload = (req: Request, res: Response, next: NextFunction) => {
  let { error } = emailVerificationPayloadSchema.validate(req.body);

  if (error) {
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} ${error.details[0].message}`,
    });
  }

  next();
};
