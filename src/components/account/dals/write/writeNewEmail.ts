import { Var } from '../../../../global/var';
import { accountModel } from '../../models';
import { logger } from '../../../../global/services';

export const writeNewEmail = async (email: string, newEmail: string) => {
  try {
    const [updatedRowsCount] = await accountModel.update(
      {
        email: newEmail,
        is_email_verified: false,
      },
      {
        where: { email },
      },
    );

    if (updatedRowsCount === 0) {
      logger.warn('Email update failed: Account not found', { email, newEmail });
      return {
        success: false,
        message: `${Var.app.emoji.failure} Account not found`,
        payload: null,
      };
    }

    logger.info('Email updated successfully', { email, newEmail });
    return {
      success: true,
      message: `${Var.app.emoji.success} Email updated`,
      payload: { email: newEmail },
    };
  } catch (error) {
    logger.error('Failed to update email', { email, newEmail, error });
    return {
      success: false,
      message: `${Var.app.emoji.failure} Failed to update email. Please contact ${Var.app.contact.email}`,
      payload: error,
    };
  }
};
