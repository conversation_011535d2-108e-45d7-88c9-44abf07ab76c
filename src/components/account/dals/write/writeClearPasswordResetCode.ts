import { Var } from '../../../../global/var';
import { accountModel } from '../../models';
import { logger } from '../../../../global/services';

export const writeClearPasswordResetCode = async (email: string) => {
  let isSuccessful: boolean = false;
  let returnData: any = {};

  try {
    const [updatedRowsCount] = await accountModel.update(
      {
        password_reset_code: null,
        password_reset_code_timestamp: null,
      },
      {
        where: {
          email: email,
        },
      },
    );

    if (updatedRowsCount > 0) {
      isSuccessful = true;
      returnData = {
        email,
      };

      logger.info(`Password reset token cleared for ${email}`, {
        email,
      });
    } else {
      logger.warn(`Failed to clear password reset token: Account not found`, {
        email,
      });
      returnData = new Error('Account not found');
    }
  } catch (error) {
    logger.error(`Error clearing password reset token`, {
      email,
      error,
    });
    returnData = error;
  }

  return {
    success: isSuccessful,
    message: isSuccessful
      ? `${Var.app.emoji.success} Password reset code cleared`
      : `${Var.app.emoji.failure} Password reset code not cleared. Please contact ${Var.app.contact.email}`,
    payload: returnData,
  };
};
