import { Var } from '../../../../global/var';
import { accountModel } from '../../models';
import { logger } from '../../../../global/services';

export const writeNewPassword = async (email: string, newHashedPassword: string) => {
  try {
    const [updatedRowsCount] = await accountModel.update(
      {
        password: newHashedPassword,
        password_reset_code: null,
        password_reset_code_timestamp: null,
      },
      {
        where: { email },
      },
    );

    if (updatedRowsCount === 0) {
      logger.warn('Password update failed: Account not found', { email });
      return {
        success: false,
        message: `${Var.app.emoji.failure} Account not found`,
        payload: null,
      };
    }

    logger.info('Password updated successfully', { email });
    return {
      success: true,
      message: `${Var.app.emoji.success} Password updated`,
      payload: { email },
    };
  } catch (error) {
    logger.error('Failed to update password', { email, error });
    return {
      success: false,
      message: `${Var.app.emoji.failure} Failed to update password. Please contact ${Var.app.contact.email}`,
      payload: error,
    };
  }
};
