import { accountModel } from '../../models';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

/**
 * Data Access Layer function to create a new user account
 *
 * This function handles the creation of new user accounts during the signup process.
 * It creates a new record in the accounts table with the provided information.
 *
 * Security considerations:
 * - Password must be pre-hashed before calling this function
 * - Email verification is set to false by default
 * - Account is created with default security settings
 *
 * @param name - The user's full name
 * @param email - The user's email address (must be unique)
 * @param hashedPassword - The pre-hashed password (never store plain text passwords)
 * @returns Promise<{success: boolean, message: string, payload: object}> - Operation result
 *
 * @example
 * const result = await writeNewAccount('<PERSON>', '<EMAIL>', hashedPassword);
 * if (result.success) {
 *   console.log('Account created:', result.payload.id);
 * }
 */
export const writeNewAccount = async (name: string, email: string, hashedPassword: string) => {
  try {
    // Create new account record in the database
    // Default values are set by the model (email verification = false, etc.)
    const newAccount = await accountModel.create({
      name,
      email,
      password: hashedPassword, // Pre-hashed password for security
    });

    // Extract relevant account data for response
    // Note: We don't return sensitive information like password hash
    const accountData = {
      id: newAccount.dataValues.id,
      name: newAccount.dataValues.name,
      email: newAccount.dataValues.email,
      isEmailVerified: newAccount.dataValues.is_email_verified,
    };

    // Log successful account creation for audit purposes
    logger.info('New account created successfully', { email, accountId: accountData.id });

    return {
      success: true,
      message: `${Var.app.emoji.success} New account created`,
      payload: accountData,
    };
  } catch (error) {
    logger.error('Failed to create new account', { email, error });
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not create account. Please contact ${Var.app.contact.email}`,
      payload: error,
    };
  }
};
