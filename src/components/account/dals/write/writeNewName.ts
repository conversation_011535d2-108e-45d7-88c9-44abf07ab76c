import { Var } from '../../../../global/var';
import { accountModel } from '../../models';
import { logger } from '../../../../global/services';

export const writeNewName = async (email: string, newName: string) => {
  try {
    const [updatedRowsCount] = await accountModel.update(
      { name: newName },
      {
        where: { email },
      },
    );

    if (updatedRowsCount === 0) {
      logger.warn('Name update failed: Account not found', { email });
      return {
        success: false,
        message: `${Var.app.emoji.failure} Account not found`,
        payload: null,
      };
    }

    logger.info('Name updated successfully', { email, newName });
    return {
      success: true,
      message: `${Var.app.emoji.success} Name updated`,
      payload: { newName },
    };
  } catch (error) {
    logger.error('Failed to update name', { email, error });
    return {
      success: false,
      message: `${Var.app.emoji.failure} Failed to update name. Please contact ${Var.app.contact.email}`,
      payload: error,
    };
  }
};
