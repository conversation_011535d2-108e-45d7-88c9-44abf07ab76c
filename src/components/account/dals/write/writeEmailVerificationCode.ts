import { Var } from '../../../../global/var';
import { accountModel } from '../../models';
import { logger } from '../../../../global/services';

export const writeEmailVerificationCode = async (email: string, hashedToken: string | null, expiresAt: Date | null) => {
  let isSuccessful: boolean = false;
  let returnData: any = {};

  try {
    const [updatedRowsCount] = await accountModel.update(
      {
        email_verification_code: hashedToken,
        email_verification_code_timestamp: expiresAt,
      },
      {
        where: {
          email: email,
        },
      },
    );

    if (updatedRowsCount > 0) {
      isSuccessful = true;
      returnData = {
        email,
        expiresAt,
      };

      logger.info(`Email verification token ${hashedToken ? 'stored' : 'cleared'} for ${email}`, {
        expiresAt,
        email,
        action: hashedToken ? 'store' : 'clear',
      });
    } else {
      logger.warn(`Failed to ${hashedToken ? 'store' : 'clear'} email verification token: Account not found`, {
        email,
      });
      returnData = new Error('Account not found');
    }
  } catch (error) {
    logger.error(`Error ${hashedToken ? 'storing' : 'clearing'} email verification token`, {
      email,
      error,
    });
    returnData = error;
  }

  return {
    success: isSuccessful,
    message: isSuccessful
      ? `${Var.app.emoji.success} Email verification code ${hashedToken ? 'saved' : 'cleared'}`
      : `${Var.app.emoji.failure} Email verification code not ${hashedToken ? 'saved' : 'cleared'}. Please contact ${Var.app.contact.email}`,
    payload: returnData,
  };
};
