import { Var } from '../../../../global/var';
import { accountModel } from '../../models';
import { logger } from '../../../../global/services';

export const writePasswordResetCode = async (email: string, hashedToken: string, expiresAt: Date) => {
  let isSuccessful: boolean = false;
  let returnData: any = {};

  try {
    const [updatedRowsCount] = await accountModel.update(
      {
        password_reset_code: hashedToken,
        password_reset_code_timestamp: expiresAt,
      },
      {
        where: {
          email: email,
          is_email_verified: true,
        },
      },
    );

    if (updatedRowsCount > 0) {
      isSuccessful = true;
      returnData = {
        email,
        expiresAt,
      };

      logger.info(`Password reset token stored for ${email}`, {
        expiresAt,
        email,
      });
    } else {
      logger.warn(`Failed to store password reset token: Account not found or not verified`, {
        email,
      });
      returnData = new Error('Account not found or not verified');
    }
  } catch (error) {
    logger.error(`Error storing password reset token`, {
      email,
      error,
    });
    returnData = error;
  }

  return {
    success: isSuccessful,
    message: isSuccessful
      ? `${Var.app.emoji.success} Password reset code saved`
      : `${Var.app.emoji.failure} Password reset code not saved. Please contact ${Var.app.contact.email}`,
    payload: returnData,
  };
};
