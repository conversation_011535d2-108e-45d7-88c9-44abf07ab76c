import { accountModel } from '../../models';

/**
 * Data Access Layer function to retrieve an account by its unique ID
 *
 * This function is used for:
 * - Session validation and user authentication
 * - Retrieving user profile information
 * - Account management operations
 * - Authorization checks
 *
 * @param accountId - The unique UUID of the account to retrieve
 * @returns Promise<Account | null> - The account object if found, null otherwise
 *
 * @example
 * const account = await readAccountById('123e4567-e89b-12d3-a456-************');
 * if (account) {
 *   console.log('Account name:', account.dataValues.name);
 * }
 */
export const readAccountById = async (accountId: string) => {
  // Query the database for an account with the specified ID
  // Uses the primary key for optimal performance
  const account = await accountModel.findOne({
    where: {
      id: accountId,
    },
  });

  return account;
};
