import * as postmark from 'postmark';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

export const mailAccountChangeNotification = async (email: string, field: string, name: string) => {
  try {
    const postmarkClient = new postmark.Client(Var.postmark.token);

    const result = await new Promise((resolve, reject) => {
      postmarkClient.sendEmailWithTemplate(
        {
          From: `${Var.app.name} no-reply@${Var.app.domain}`,
          TemplateId: Var.postmark.template.accountChangeNotification.id,
          To: email,
          TemplateModel: {
            name,
            field,
            app: Var.app,
          },
        },
        (error, success) => {
          if (error) {
            logger.error('Failed to send account change notification', {
              email,
              field,
              error: error.message,
              errorCode: error.code,
            });
            reject(error);
          } else if (success) {
            logger.info('Account change notification sent successfully', {
              email,
              field,
              messageId: success.MessageID,
            });
            resolve(success);
          } else {
            logger.error('Account change notification failed with no error or success response', { email, field });
            reject(new Error('No response from email service'));
          }
        },
      );
    });

    return {
      success: true,
      message: `${Var.app.emoji.success} Account change confirmation sent`,
      payload: result,
    };
  } catch (error) {
    logger.error('Error sending account change notification', {
      email,
      field,
      error,
    });
    return {
      success: false,
      message: `${Var.app.emoji.failure} Account change confirmation not sent. Please contact ${Var.app.contact.email}`,
      payload: error,
    };
  }
};
