import * as postmark from 'postmark';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

export const mailPasswordResetCode = async (name: string, email: string, resetCode: string) => {
  try {
    const postmarkClient = new postmark.Client(Var.postmark.token);
    const templateId: number = Var.postmark.template.passwordResetCode.id;

    logger.info('Sending 6-digit password reset code', {
      email,
      codeLength: resetCode.length,
    });

    const result = await new Promise((resolve, reject) => {
      postmarkClient.sendEmailWithTemplate(
        {
          From: `${Var.app.name} <no-reply@${Var.app.domain}>`,
          TemplateId: templateId,
          To: email,
          TemplateModel: {
            resetCode: resetCode,
            name: name,
            app: Var.app,
            expiresIn: '15 minutes',
          },
          MessageStream: 'outbound', // Use the outbound message stream
          TrackOpens: true,
        },
        (error, success) => {
          if (error) {
            logger.error(`Failed to send password reset email`, {
              email,
              error: error.message,
              errorCode: error.code,
            });
            reject(error);
          } else if (success) {
            logger.info(`Password reset email sent successfully`, {
              email,
              messageId: success.MessageID,
            });
            resolve(success);
          } else {
            logger.error(`Password reset email failed with no error or success response`, { email });
            reject(new Error('No response from email service'));
          }
        },
      );
    });

    return {
      success: true,
      message: `${Var.app.emoji.success} Password reset instructions sent to ${email}`,
      payload: result,
    };
  } catch (error) {
    logger.error(`Error sending password reset email`, {
      email,
      error,
    });

    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not send password reset instructions. Please try again later or contact ${Var.app.contact.email}`,
      payload: error,
    };
  }
};
