import * as postmark from 'postmark';

import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

export const mailEmailVerificationCode = async (name: string, email: string, verificationCode: string) => {
  try {
    const postmarkClient = new postmark.Client(Var.postmark.token);
    const templateId: number = Var.postmark.template.emailVerificationCode.id;

    logger.info('Sending 6-digit email verification code', {
      email,
      codeLength: verificationCode.length,
    });

    const result = await new Promise((resolve, reject) => {
      postmarkClient.sendEmailWithTemplate(
        {
          From: `${Var.app.name} <no-reply@${Var.app.domain}>`,
          TemplateId: templateId,
          To: email,
          TemplateModel: {
            verificationCode: verificationCode,
            name: name,
            app: Var.app,
            expiresIn: '24 hours',
          },
          MessageStream: 'outbound',
          TrackOpens: true,
        },
        (error, success) => {
          if (error) {
            logger.error('Failed to send verification email', {
              email,
              error: error.message,
              errorCode: error.code,
            });
            reject(error);
          } else if (success) {
            logger.info('Verification email sent successfully', {
              email,
              messageId: success.MessageID,
            });
            resolve(success);
          } else {
            logger.error('Verification email failed with no error or success response', {
              email,
            });
            reject(new Error('No response from email service'));
          }
        },
      );
    });

    return {
      success: true,
      message: `${Var.app.emoji.success} Verification code sent to ${email}`,
      payload: result,
    };
  } catch (error) {
    logger.error('Error sending verification email', {
      email,
      error,
    });

    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not send verification code. Please try again later or contact ${Var.app.contact.email}`,
      payload: error,
    };
  }
};
