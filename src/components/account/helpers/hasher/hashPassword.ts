import { hash, argon2id } from 'argon2';
import { logger } from '../../../../global/services';

/**
 * Secure password hashing utility using Argon2id algorithm
 *
 * This function implements industry-standard password hashing using Argon2id,
 * which is the recommended algorithm for password hashing as of 2024.
 * Argon2id provides resistance against both side-channel and GPU-based attacks.
 *
 * Security features:
 * - Uses Argon2id variant (hybrid of Argon2i and Argon2d)
 * - Memory-hard function resistant to ASIC attacks
 * - Configurable time, memory, and parallelism parameters
 * - Cryptographically secure salt generation
 * - Comprehensive error handling and logging
 *
 * Configuration rationale:
 * - memoryCost: 65536 (64 MB) - Balances security and performance
 * - timeCost: 3 iterations - Provides adequate computational cost
 * - parallelism: 4 threads - Utilizes modern multi-core processors
 * - saltLength: 32 bytes - Provides 256 bits of entropy
 * - hashLength: 32 bytes - Standard for cryptographic hashes
 *
 * @param password - The plain text password to hash (never logged or stored)
 * @returns Promise<string> - The Argon2id hash string including salt and parameters
 * @throws Error - If hashing fails due to system or memory constraints
 *
 * @example
 * const hashedPassword = await hashPassword('userPassword123');
 * // Returns: $argon2id$v=19$m=65536,t=3,p=4$salt$hash
 */
export const hashPassword = async (password: string): Promise<string> => {
  try {
    // Hash password using Argon2id with security-optimized parameters
    const hashedPassword: string = await hash(password, {
      type: argon2id, // Hybrid variant for maximum security
      memoryCost: 65536, // 64 MB memory usage (2^16 KB)
      timeCost: 3, // 3 iterations for computational cost
      parallelism: 4, // 4 parallel threads
      saltLength: 32, // 32-byte (256-bit) cryptographic salt
      hashLength: 32, // 32-byte (256-bit) hash output
    });

    return hashedPassword;
  } catch (error) {
    // Log error for debugging but don't expose sensitive details
    logger.error('Error hashing password', { error });
    // Throw generic error to prevent information leakage
    throw new Error('Failed to hash password');
  }
};
