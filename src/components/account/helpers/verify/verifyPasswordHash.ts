import * as argon from 'argon2';
import { logger } from '../../../../global/services';

/**
 * Secure password verification utility using Argon2
 *
 * This function verifies a plain text password against an Argon2 hash.
 * It automatically detects the Argon2 variant (argon2i, argon2d, or argon2id)
 * and uses the parameters embedded in the hash string.
 *
 * Security features:
 * - Timing-safe comparison to prevent timing attacks
 * - Automatic parameter extraction from hash string
 * - Supports all Argon2 variants for backward compatibility
 * - Fail-safe behavior (returns false on any error)
 * - No sensitive data logging
 *
 * The Argon2 library handles:
 * - Salt extraction from the hash string
 * - Parameter parsing (memory cost, time cost, parallelism)
 * - Constant-time comparison of hash values
 * - Memory-hard computation with the same parameters as original hash
 *
 * @param hashedPassword - The stored Argon2 hash string (includes salt and parameters)
 * @param plainPassword - The plain text password to verify (never logged)
 * @returns Promise<boolean> - true if password matches, false otherwise
 *
 * @example
 * const isValid = await verifyPasswordHash(storedHash, userInputPassword);
 * if (isValid) {
 *   // Password is correct, proceed with authentication
 * }
 */
export const verifyPasswordHash = async (hashedPassword: string, plainPassword: string): Promise<boolean> => {
  try {
    // Verify password using Argon2's built-in verification
    // This automatically extracts salt and parameters from the hash string
    const isPasswordValid: boolean = await argon.verify(hashedPassword, plainPassword);
    return isPasswordValid;
  } catch (error) {
    // Log error for debugging but don't expose sensitive information
    logger.error('Error verifying password', { error });
    // Return false on any error for fail-safe security behavior
    return false;
  }
};
