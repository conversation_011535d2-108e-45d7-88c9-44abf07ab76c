import { Request, Response } from 'express';
import { logger } from '../../../../global/services';
import { writeAccountLockStatus } from '../../dals/write/writeAccountLockStatus';
import { accountModel } from '../../models';

/**
 * Authentication helper function to establish user session after successful login
 *
 * This function handles the session creation and account status updates that occur
 * after a user has been successfully authenticated. It sets up the session with
 * security metadata and updates account tracking information.
 *
 * Security features:
 * - Creates secure session with account ID
 * - Records session creation timestamp for timeout management
 * - Stores user agent and IP for session validation
 * - Updates last login timestamp for account monitoring
 * - Clears any existing account locks
 * - Comprehensive audit logging
 *
 * Session security:
 * - Session ID is automatically generated by Express session middleware
 * - Session data is stored server-side (Redis) for security
 * - Client only receives session cookie with secure flags
 * - User agent and IP are stored for session hijacking detection
 *
 * @param req - Express request object containing session and headers
 * @param _res - Express response object (unused but required for consistency)
 * @param accountId - The unique UUID of the authenticated account
 * @returns Promise<void> - Completes when session is established
 *
 * @example
 * // After successful password verification
 * await login(req, res, account.dataValues.id);
 * // User is now logged in with active session
 */
export const login = async (req: Request, _res: Response, accountId: string) => {
  try {
    // Establish user session with security metadata
    req.session!.accountId = accountId; // Primary account identifier
    req.session!.createdAt = Date.now(); // Session creation timestamp
    req.session!.userAgent = req.headers['user-agent']; // Browser fingerprint
    req.session!.ipAddress = req.ip; // Client IP for validation

    // Update account's last login timestamp for monitoring
    await updateLastLoginTimestamp(accountId);

    // Clear any existing account locks (successful login resets lock status)
    await writeAccountLockStatus(accountId, false, null);

    // Log successful login for security audit trail
    logger.info(`User logged in successfully`, {
      accountId,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });
  } catch (error) {
    // Log error but don't throw - login should still succeed if possible
    logger.error(`Error in login helper`, { accountId, error });
  }
};

async function updateLastLoginTimestamp(accountId: string): Promise<void> {
  try {
    await accountModel.update(
      {
        last_login_at: new Date(),
        login_attempts: 0,
      },
      {
        where: { id: accountId },
      },
    );
  } catch (error) {
    logger.error(`Failed to update last login timestamp`, { accountId, error });
  }
}
