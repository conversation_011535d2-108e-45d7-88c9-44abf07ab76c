import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';
import { Var } from '../../../../global/var';

const createSurveyPayloadSchema = Joi.object({
  type: Joi.string().valid('sensePrice', 'senseChoice', 'sensePoll', 'senseQuery', 'sensePriority').required(),
  title: Joi.string().required(),
  distribution: Joi.string().required(),
  embedUrl: Joi.string()
    .uri({ scheme: ['https', 'http'] })
    .regex(/^(https?:\/\/)?(localhost|127\.0\.0\.1)(:[0-9]+)?(\/.*)?$|^https:\/\/.*$/)
    .allow(''),
  tags: Joi.array().items(Joi.string()).min(0).max(1024).required(),
  config: Joi.object().required(),
  respondentDetails: Joi.array()
    .items(
      Joi.object({
        label: Joi.string().required(),
        value: Joi.string().required(),
        inputType: Joi.string().valid('text', 'email', 'dropdown', 'radio', 'checkbox', 'number').required(),
        required: Joi.boolean().optional(),
        placeholder: Joi.string().optional().allow(''),
        options: Joi.when('inputType', {
          is: Joi.string().valid('dropdown', 'radio', 'checkbox'),
          then: Joi.array()
            .items(
              Joi.object({
                value: Joi.string().required(),
                label: Joi.string().required(),
              }),
            )
            .optional(),
          otherwise: Joi.optional(),
        }),
      }),
    )
    .min(0)
    .max(1024)
    .required(),
});

export const validateCreateSurveyPayload = (req: Request, res: Response, next: NextFunction) => {
  let { error } = createSurveyPayloadSchema.validate(req.body);

  if (error) {
    console.log(`${Var.app.emoji.failure} Create survey payload not valid`);
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} ${error.details[0].message}`,
    });
  }

  console.log(`${Var.app.emoji.success} Create survey payload valid`);
  next();
};
