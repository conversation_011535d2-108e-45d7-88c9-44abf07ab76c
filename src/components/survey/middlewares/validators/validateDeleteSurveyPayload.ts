import Jo<PERSON> from "joi";
import { Request, Response, NextFunction } from "express";
import { Var } from "../../../../global/var";

const deleteSurveyPayloadSchema = Joi.object({
  surveyId: Joi.string().uuid().required(),
});

export const validateDeleteSurveyPayload = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let { error } = deleteSurveyPayloadSchema.validate({
    surveyId: req.params.surveyId,
  });

  if (error) {
    console.log(`${Var.app.emoji.failure} Delete survey payload not valid`);
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} ${error.details[0].message}`,
    });
  }

  console.log(`${Var.app.emoji.success} Delete survey payload valid`);
  next();
};
