import { Request, Response, NextFunction } from "express";
import { Var } from "../../../../global/var";
import { readSurveyById } from "../../dals";

export const formatGetShareUrlPayload = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const surveyId = req.params.surveyId;
  
  // Check if the survey exists and belongs to the user
  const survey = await readSurveyById(surveyId);
  if (!survey) {
    console.log(`${Var.app.emoji.failure} Survey not found`);
    return res.status(404).json({
      success: false,
      message: `${Var.app.emoji.failure} Survey not found`,
    });
  }

  // Store the survey in res.locals for the controller to use
  res.locals.survey = survey;

  next();
};
