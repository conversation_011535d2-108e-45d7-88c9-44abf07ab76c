import { Request, Response, NextFunction } from 'express';

export const formatCreateSurveyPayload = (req: Request, res: Response, next: NextFunction) => {
  const { type, title, distribution, embedUrl, tags, config, respondentDetails } = req.body;

  res.locals.type = type.trim();
  res.locals.title = title.trim();
  res.locals.distribution = distribution.trim();
  res.locals.embedUrl = embedUrl.trim();
  res.locals.tags = tags;
  res.locals.config = config;
  res.locals.respondentDetails = respondentDetails;

  next();
};
