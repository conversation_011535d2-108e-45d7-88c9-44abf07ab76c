import { Request, Response, NextFunction } from 'express';
import { readSurveyByPublicKey } from '../../dals';
import { Var } from '../../../../global/var';

/**
 * Middleware to validate survey access based on distribution settings
 * Checks if the requesting origin is allowed to access the survey
 */
export const BlockRequestByDistribution = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const publicKey = req.params.publicKey;
    const origin = res.locals.origin || '';

    console.log(`${Var.app.emoji.success} Processing request for survey: ${publicKey}, Origin: "${origin}"`);

    // Check if the survey is already in res.locals (from cache)
    let survey = res.locals.survey;

    // If not in res.locals, fetch from database
    if (!survey) {
      console.log(`${Var.app.emoji.warning} Survey not in cache, fetching from database: ${publicKey}`);
      survey = await readSurveyByPublicKey(publicKey);

      if (!survey) {
        console.log(`${Var.app.emoji.failure} Survey not found: ${publicKey}`);
        return res.status(404).json({
          success: false,
          message: 'Survey not found',
        });
      }
    }

    const distribution = survey.distribution;
    const embedUrl = survey.embed_url || '';

    // Check if the origin is allowed based on distribution settings
    let isOriginAllowed = false;

    /**
     * Check if the origin domain matches the embed URL domain
     */
    const isOriginMatchingEmbedUrl = (): boolean => {
      // Return false if either URL is empty or not provided
      if (!embedUrl || !origin) {
        console.log(`${Var.app.emoji.warning} Empty URL detected: embedUrl=${embedUrl}, origin=${origin}`);
        return false;
      }

      try {
        // Ensure URLs have proper protocol
        const normalizeUrl = (url: string): string => {
          if (!url.startsWith('http://') && !url.startsWith('https://')) {
            return `https://${url}`;
          }
          return url;
        };

        const embedUrlDomain = new URL(normalizeUrl(embedUrl)).hostname;
        const originDomain = new URL(normalizeUrl(origin)).hostname;

        console.log(`${Var.app.emoji.success} Comparing domains: embedUrl=${embedUrlDomain}, origin=${originDomain}`);
        return embedUrlDomain === originDomain;
      } catch (error) {
        console.error(`${Var.app.emoji.failure} Error parsing URL:`, error);
        return false;
      }
    };

    // Check if the origin is from sensefolks.com
    const isSenseFolksOrigin = origin ? origin.includes('sensefolks.com') : false;

    // Determine if the origin is allowed based on distribution type
    switch (distribution) {
      case 'embed':
        // Only allow if origin matches embed URL
        isOriginAllowed = embedUrl ? isOriginMatchingEmbedUrl() : false;
        break;

      case 'link':
        // Only allow if origin is sensefolks.com
        isOriginAllowed = isSenseFolksOrigin;
        break;

      case 'embed&link':
        // Allow if origin matches embed URL or is sensefolks.com
        isOriginAllowed = (embedUrl ? isOriginMatchingEmbedUrl() : false) || isSenseFolksOrigin;
        break;

      default:
        isOriginAllowed = false;
    }

    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.warning} Development environment detected, bypassing origin check`);
      isOriginAllowed = true;
    } else {
      console.log(`${Var.app.emoji.success} Origin allowed: ${isOriginAllowed}, Distribution: ${distribution}`);
    }

    // Block the request if origin is not allowed
    if (!isOriginAllowed) {
      console.log(`${Var.app.emoji.failure} Access denied for origin: ${origin}`);
      return res.status(403).json({
        success: false,
        message: 'Access denied based on origin',
        details: {
          origin,
          distribution,
        },
      });
    }

    // Store the survey in res.locals for the controller
    res.locals.survey = survey;
    next();
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Error in BlockRequestByDistribution:`, error);
    return res.status(500).json({
      success: false,
      message: 'Error processing request',
    });
  }
};
