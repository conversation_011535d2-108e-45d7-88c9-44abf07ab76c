import { Request, Response, NextFunction } from 'express';
import { redisClient, logger } from '../../../../global/services';

/**
 * PERFORMANCE: Remove survey from cache after deletion
 * Ensures deleted surveys are not served from cache to embedded widgets
 */
export const removeSurveyCache = async (_req: Request, res: Response, next: NextFunction) => {
  // Only remove cache on successful deletion
  if (res.statusCode !== 200) {
    return next();
  }

  try {
    const deletedSurvey = res.locals.deletedSurvey;
    
    if (!deletedSurvey) {
      logger.debug('No deleted survey data for cache removal');
      return next();
    }

    const publicKey = deletedSurvey.public_key;
    const surveyId = deletedSurvey.id;

    if (!publicKey) {
      logger.warn('Survey missing public_key for cache removal', { surveyId });
      return next();
    }

    // Remove survey from cache
    const cacheKey = `survey:${publicKey}`;
    const result = await redisClient.del(cacheKey);

    if (result === 1) {
      logger.info('Survey cache removed', {
        publicKey: publicKey.substring(0, 8) + '...',
        surveyId: surveyId.substring(0, 8) + '...',
        cacheKey,
      });
    } else {
      logger.debug('Survey cache key did not exist', {
        publicKey: publicKey.substring(0, 8) + '...',
        cacheKey,
      });
    }

  } catch (error) {
    logger.error('Failed to remove survey cache', {
      error,
      surveyData: res.locals.deletedSurvey ? {
        id: res.locals.deletedSurvey.id?.substring(0, 8) + '...',
        public_key: res.locals.deletedSurvey.public_key?.substring(0, 8) + '...',
      } : 'none',
    });
    // Don't fail the request if cache removal fails
  }

  next();
};
