import { Request, Response, NextFunction } from 'express';
import { getSurveyFromCache } from '../../../../global/services';
import { Var } from '../../../../global/var';

/**
 * Optimized middleware to retrieve survey from cache by public key
 * If found in cache, sets it in res.locals and skips database query
 * If not found, allows the request to proceed to the next middleware
 */
export const getSurveyByPublicKeyFromCache = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const publicKey = req.params.publicKey;
    if (!publicKey) {
      res.locals.fromCache = false;
      return next();
    }

    // Try to get the survey from cache
    const cachedSurvey = await getSurveyFromCache(publicKey);

    if (cachedSurvey) {
      // Only log in development mode
      if (Var.node.env === 'dev') {
        console.log(`${Var.app.emoji.success} Retrieved survey from cache: ${publicKey}`);
      }

      // Store the cached survey in res.locals for the controller
      res.locals.survey = cachedSurvey;
      res.locals.fromCache = true;

      // Skip to the controller, bypassing database query
      return next();
    }

    // If not in cache, proceed to the next middleware (which will query the database)
    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.warning} Survey not found in cache: ${publicKey}`);
    }

    res.locals.fromCache = false;
    next();
  } catch (error) {
    // Only log errors in development mode
    if (Var.node.env === 'dev') {
      console.error(`${Var.app.emoji.failure} Error retrieving survey from cache:`, error);
    }

    // Continue to the next middleware even if cache retrieval fails
    res.locals.fromCache = false;
    next();
  }
};
