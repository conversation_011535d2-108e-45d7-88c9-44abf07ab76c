import { Router } from 'express';
import {
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
} from '../../../../global/middlewares';
import { validateUpdateSurveyPayload, formatUpdateSurveyPayload, updateSurveyCache } from '../../middlewares';
import { GenerateApiVersionPath } from '../../../../global/helpers';
import { updateSurveyController } from '../../controllers';

export const updateSurveyRoute = Router();

updateSurveyRoute.put(
  `${GenerateApiVersionPath()}surveys/:surveyId`,
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  validateUpdateSurveyPayload,
  formatUpdateSurveyPayload,
  updateSurveyController,
  updateSurveyCache,
);
