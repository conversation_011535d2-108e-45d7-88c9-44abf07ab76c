import { surveyModel } from '../../models';
import { Var } from '../../../../global/var';

export const writeSurveyDeletion = async (surveyId: string, accountId: string) => {
  let isSuccessful: boolean = false;
  let returnData: any;

  await surveyModel
    .update(
      { is_deleted: true },
      {
        where: {
          id: surveyId,
          account_id: accountId,
        },
      },
    )
    .then((updatedSurvey: any) => {
      isSuccessful = updatedSurvey[0] > 0;
      returnData = updatedSurvey;
    })
    .catch((err: any) => (returnData = err));

  return {
    success: isSuccessful,
    message: isSuccessful ? `${Var.app.emoji.success} Survey deleted` : `${Var.app.emoji.failure} Could not delete survey. Please contact ${Var.app.contact.email}`,
    payload: returnData,
  };
};
