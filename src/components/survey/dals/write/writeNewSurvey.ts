import { surveyModel } from '../../models';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

export const writeNewSurvey = async (
  type: string,
  title: string,
  distribution: string,
  embedUrl: string,
  tags: string[],
  config: object,
  respondentDetails: any[],
  accountId: string,
) => {
  try {
    const newSurvey = await surveyModel.create({
      type,
      title,
      distribution,
      embed_url: embedUrl,
      tags,
      config,
      respondent_details: respondentDetails,
      meta: {},
      account_id: accountId,
    });

    const surveyData = {
      id: newSurvey.dataValues.id,
      type: newSurvey.dataValues.type,
      title: newSurvey.dataValues.title,
      distribution: newSurvey.dataValues.distribution,
      embed_url: newSurvey.dataValues.embed_url,
      share_url: newSurvey.dataValues.share_url,
      tags: newSurvey.dataValues.tags,
      config: newSurvey.dataValues.config,
      respondentDetails: newSurvey.dataValues.respondent_details,
    };

    logger.info('New survey created successfully', {
      surveyId: surveyData.id,
      accountId,
      title,
    });

    return {
      success: true,
      message: `${Var.app.emoji.success} New survey created`,
      payload: surveyData,
    };
  } catch (error) {
    logger.error('Failed to create new survey', { accountId, title, error });
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not create survey. Please contact ${Var.app.contact.email}`,
      payload: error,
    };
  }
};
