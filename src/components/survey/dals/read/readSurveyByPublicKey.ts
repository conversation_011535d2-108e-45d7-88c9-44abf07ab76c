import { surveyModel } from '../../models';
import { Var } from '../../../../global/var';
import { Op } from 'sequelize';

/**
 * Interface for the survey data returned by the database
 */
export interface SurveyData {
  id: string;
  type: string;
  title: string;
  distribution: string;
  embed_url: string;
  config: Record<string, any>;
  respondent_details: Array<any>;
  public_key: string;
}

// Specific attributes needed for the survey request API
const REQUIRED_ATTRIBUTES = ['type', 'distribution', 'embed_url', 'config', 'respondent_details', 'public_key'];

/**
 * Fetches a survey by its public key with optimized query
 * @param publicKey - The public key of the survey
 * @returns The survey data or null if not found
 */
export const readSurveyByPublicKey = async (publicKey: string): Promise<SurveyData | null> => {
  if (!publicKey) {
    return null;
  }

  try {
    // Optimized query with specific attributes and indexing
    const survey = await surveyModel.findOne({
      // Only select the attributes we need
      attributes: REQUIRED_ATTRIBUTES,
      // Optimized where clause
      where: {
        public_key: publicKey,
        // Combined condition for better performance
        [Op.and]: [{ is_deleted: false }, { is_disabled: false }],
      },
      // Add raw option for better performance when we don't need Sequelize model methods
      // raw: true, // Uncomment if you don't need Sequelize model methods
    });

    // Return null if no survey is found
    if (!survey) {
      return null;
    }

    // Return the survey data
    return survey.toJSON() as SurveyData;
  } catch (error) {
    // Log error in development mode
    if (Var.node.env === 'dev') {
      console.error(`${Var.app.emoji.failure} Error fetching survey by public key:`, error);
    }
    throw error;
  }
};
