import { surveyModel } from '../../models';

/**
 * Data Access Layer function to retrieve all surveys belonging to a specific account
 *
 * This function fetches all non-deleted surveys for a given account ID.
 * Used primarily for displaying the user's survey dashboard and management interface.
 *
 * Query optimizations:
 * - Only selects necessary attributes to reduce data transfer
 * - Filters out deleted surveys for better UX
 * - Orders by updated_at DESC to show most recently modified surveys first
 *
 * @param accountId - The unique UUID of the account whose surveys to retrieve
 * @returns Promise<Survey[]> - Array of survey objects belonging to the account
 *
 * @example
 * const userSurveys = await readSurveysByAccountId('123e4567-e89b-12d3-a456-************');
 * console.log(`User has ${userSurveys.length} surveys`);
 */
export const readSurveysByAccountId = async (accountId: string) => {
  // Query all surveys for the specified account
  const surveys = await surveyModel.findAll({
    // Select only the attributes needed for the survey list view
    attributes: ['id', 'type', 'title', 'tags', 'response_count', 'created_at', 'embed_url', 'share_key', 'public_key', 'meta'],
    where: {
      account_id: accountId,
      is_deleted: false, // Exclude soft-deleted surveys
    },
    // Order by most recently updated first for better UX
    order: [['updated_at', 'DESC']],
  });

  return surveys;
};
