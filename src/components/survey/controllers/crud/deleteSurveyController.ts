import { Request, Response, NextFunction } from 'express';
import { writeSurveyDeletion, readSurveyById } from '../../dals';
import { verifyResourceOwnership } from '../../../security/helpers';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

export const deleteSurveyController = async (req: Request, res: Response, next: NextFunction) => {
  const surveyId = res.locals.surveyId;
  const accountId = res.locals.accountId;

  logger.info('Survey deletion attempt', {
    surveyId: surveyId.substring(0, 8) + '...',
    accountId: accountId.substring(0, 8) + '...',
    ip: req.ip,
  });

  // SECURITY FIX: Double authorization check - middleware + controller level
  const isOwner = await verifyResourceOwnership('survey', surveyId, accountId);
  if (!isOwner) {
    logger.warn('Unauthorized survey deletion attempt', {
      surveyId: surveyId.substring(0, 8) + '...',
      accountId: accountId.substring(0, 8) + '...',
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });
    return res.status(403).json({
      success: false,
      message: 'Access denied',
    });
  }

  // Get survey data before deletion for cache removal
  const surveyToDelete = await readSurveyById(surveyId);

  const deletionResult = await writeSurveyDeletion(surveyId, accountId);

  if (!deletionResult.success) {
    return res.status(400).json({
      success: false,
      message: deletionResult.message,
    });
  }

  // Store survey data for cache removal middleware
  res.locals.deletedSurvey = surveyToDelete;

  res.status(200).json({
    success: true,
    message: `${Var.app.emoji.success} Survey deleted`,
    payload: {},
  });

  // Continue to the next middleware (removeSurveyCache)
  next();
};
