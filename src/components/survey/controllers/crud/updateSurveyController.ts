import { Request, Response, NextFunction } from 'express';
import { writeSurveyUpdate, readSurveyById } from '../../dals';
import { verifyResourceOwnership } from '../../../security/helpers';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

export const updateSurveyController = async (req: Request, res: Response, next: NextFunction) => {
  const surveyId = res.locals.surveyId;
  const accountId = res.locals.accountId;
  const attribute = res.locals.attribute;
  const value = res.locals.value;

  logger.info('Survey update attempt', {
    surveyId: surveyId.substring(0, 8) + '...',
    accountId: accountId.substring(0, 8) + '...',
    attribute,
    ip: req.ip,
  });

  // SECURITY FIX: Double authorization check - middleware + controller level
  const isOwner = await verifyResourceOwnership('survey', surveyId, accountId);
  if (!isOwner) {
    logger.warn('Unauthorized survey update attempt', {
      surveyId: surveyId.substring(0, 8) + '...',
      accountId: accountId.substring(0, 8) + '...',
      attribute,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });
    return res.status(403).json({
      success: false,
      message: 'Access denied',
    });
  }

  // Check if the survey exists and belongs to the user
  const survey = await readSurveyById(surveyId);
  if (!survey) {
    logger.warn('Survey not found during update', { surveyId });
    return res.status(404).json({
      success: false,
      message: `${Var.app.emoji.failure} Survey not found`,
    });
  }

  // Update the survey
  const updateResult = await writeSurveyUpdate(surveyId, accountId, attribute, value);

  if (!updateResult.success) {
    return res.status(400).json({
      success: false,
      message: updateResult.message,
    });
  }

  // Get updated survey data for caching
  const updatedSurvey = await readSurveyById(surveyId);
  res.locals.updatedSurvey = updatedSurvey;

  res.status(200).json({
    success: true,
    message: `${Var.app.emoji.success} Survey ${attribute === 'config' ? 'settings' : attribute} updated`,
  });

  // Continue to cache middleware
  next();
};
