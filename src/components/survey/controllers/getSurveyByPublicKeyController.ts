import { Request, Response } from 'express';
import { Var } from '../../../global/var';

/**
 * Controller to fetch survey configuration by public key
 * Returns the survey config and respondent details
 */
export const getSurveyByPublicKeyController = async (_req: Request, res: Response) => {
  try {
    // The survey is already fetched and validated by the middleware
    const survey = res.locals.survey;

    if (!survey) {
      return res.status(404).json({
        success: false,
        message: 'Survey not found',
      });
    }

    // Format the response data
    const surveyData = {
      config: survey.config || {},
      respondentDetails: survey.respondent_details || [],
      type: survey.type,
      publicKey: survey.public_key,
    };

    // Return successful response
    return res.status(200).json({
      success: true,
      message: 'Survey configuration retrieved successfully',
      payload: surveyData,
    });
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Error in getSurveyByPublicKeyController:`, error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve survey configuration',
    });
  }
};
