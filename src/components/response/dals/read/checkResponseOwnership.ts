import { responseModel } from '../../models';
import { logger } from '../../../../global/services';

export const checkResponseOwnership = async (responseId: string, accountId: string): Promise<boolean> => {
  try {
    const response = await responseModel.findOne({
      attributes: ['id'],
      where: {
        id: responseId,
        account_id: accountId,
      },
    });

    return !!response;
  } catch (error) {
    logger.error('Error checking response ownership', { responseId, accountId, error });
    return false;
  }
};
