import { responseModel } from '../../../response/models';
import { logger } from '../../../../global/services';
import { Var } from '../../../../global/var';

/**
 * Data Access Layer function to retrieve survey responses and analytics data
 *
 * This function fetches all non-discarded responses for a specific survey
 * belonging to the specified account. Used for displaying response analytics
 * and generating insights from survey data.
 *
 * Security features:
 * - Validates account ownership by including account_id in query
 * - Excludes discarded responses to maintain data quality
 * - Comprehensive error handling with logging
 *
 * Performance considerations:
 * - Orders by creation date (newest first) for better UX
 * - Selects only necessary attributes to reduce data transfer
 * - Uses indexed fields (survey_id, account_id) for optimal query performance
 *
 * @param surveyId - The unique UUID of the survey whose responses to retrieve
 * @param accountId - The unique UUID of the account that owns the survey
 * @returns Promise<{success: boolean, message: string, payload: any}> - Operation result with responses
 *
 * @example
 * const result = await readResponsesAndAnalytics(surveyId, accountId);
 * if (result.success) {
 *   console.log(`Found ${result.payload.length} responses`);
 * }
 */
export const readResponsesAndAnalytics = async (surveyId: string, accountId: string) => {
  try {
    // Query responses with security and quality filters
    const responses = await responseModel.findAll({
      where: {
        survey_id: surveyId,
        account_id: accountId, // Security: Ensure account owns the survey
        is_discarded: false, // Quality: Exclude discarded/invalid responses
      },
      // Select only the attributes needed for analytics
      attributes: ['id', 'response_data', 'created_at'],
      // Order by newest first for better user experience
      order: [['created_at', 'DESC']],
    });

    return {
      success: true,
      message: `${Var.app.emoji.success} Responses retrieved`,
      payload: responses,
    };
  } catch (error) {
    // Log error for debugging and monitoring
    logger.error('Error retrieving responses:', error);
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not retrieve responses`,
      payload: error,
    };
  }
};
