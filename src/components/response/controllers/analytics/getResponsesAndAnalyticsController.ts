import { Request, Response } from 'express';

import { readResponsesAndAnalytics } from '../../dals';
import { verifyResourceOwnership } from '../../../security/helpers';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

export const getResponsesAndAnalyticsController = async (req: Request, res: Response) => {
  const surveyId = res.locals.surveyId;
  const accountId = res.locals.accountId;

  logger.info('Responses and analytics request', {
    surveyId: surveyId.substring(0, 8) + '...',
    accountId: accountId.substring(0, 8) + '...',
    ip: req.ip,
  });

  // SECURITY FIX: Double authorization check - middleware + controller level
  const isOwner = await verifyResourceOwnership('survey', surveyId, accountId);
  if (!isOwner) {
    logger.warn('Unauthorized responses access attempt', {
      surveyId: surveyId.substring(0, 8) + '...',
      accountId: accountId.substring(0, 8) + '...',
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });
    return res.status(403).json({
      success: false,
      message: 'Access denied',
    });
  }

  const result = await readResponsesAndAnalytics(surveyId, accountId);

  if (!result.success) {
    return res.status(400).json({
      success: false,
      message: result.message,
    });
  }

  const responses = result.payload as any[];

  if (responses.length === 0) {
    return res.status(200).json({
      success: true,
      message: `${Var.app.emoji.warning} No responses found for this survey`,
      payload: {
        totalResponses: 0,
        avgCompletionTime: 0,
        responsesByDay: {},
        countryDistribution: {},
      },
    });
  }

  const totalResponses = responses.length;
  const avgCompletionTime = 0;

  const responsesByDay: any = {};
  responses.forEach((response: any) => {
    const day = response.created_at.toISOString().split('T')[0];
    responsesByDay[day] = (responsesByDay[day] || 0) + 1;
  });

  const countryDistribution: any = {};

  return res.status(200).json({
    success: true,
    message: `${Var.app.emoji.success} Responses and analytics retrieved`,
    payload: {
      totalResponses,
      avgCompletionTime,
      responsesByDay,
      countryDistribution,
    },
  });
};
