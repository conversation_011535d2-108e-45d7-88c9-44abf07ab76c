import { Request, Response } from 'express';
import { writeNewWaitlistEntry } from '../dals';
import { Var } from '../../../global/var';
import { logger } from '../../../global/services';

/**
 * Controller to handle adding email addresses to the waitlist
 *
 * This controller processes POST requests to add email addresses to the waitlist.
 * It's designed to be used by website widgets where interested customers can
 * enter their email addresses.
 *
 * Features:
 * - Accepts email and optional metadata (source, location)
 * - <PERSON><PERSON> duplicate email entries gracefully
 * - Logs all attempts for analytics
 * - Returns consistent API responses
 * - Includes security logging for monitoring
 *
 * @param req - Express request object
 * @param res - Express response object
 */
export const addToWaitlistController = async (req: Request, res: Response) => {
  const { email, meta } = res.locals;

  // Log waitlist attempt for analytics and monitoring
  logger.info('Waitlist entry attempt', {
    email: email.substring(0, 3) + '***@' + email.split('@')[1],
    ip: req.ip,
    userAgent: req.headers['user-agent'],
    origin: res.locals.origin || req.headers.origin,
    country: res.locals.country || 'unknown',
    hasMetadata: Object.keys(meta).length > 0,
  });

  // DEBUG: Console output in development only
  if (Var.node.env === 'dev') {
    console.log(`[DEV] Waitlist attempt: ${email.substring(0, 3)}***@${email.split('@')[1]}`);
  }

  try {
    // Attempt to create the waitlist entry
    const result = await writeNewWaitlistEntry(email, meta);

    if (!result.success) {
      // Handle known errors (duplicate email, validation, etc.)
      logger.warn('Waitlist entry failed', {
        email: email.substring(0, 3) + '***@' + email.split('@')[1],
        reason: result.message,
        ip: req.ip,
      });

      // DEBUG: Console output in development only
      if (Var.node.env === 'dev') {
        console.log(`[DEV] Waitlist failed: ${result.message}`);
      }

      return res.status(400).json({
        success: false,
        message: result.message,
      });
    }

    // Success - email added to waitlist
    logger.info('Waitlist entry successful', {
      id: result.payload?.id.substring(0, 8) + '...',
      email: email.substring(0, 3) + '***@' + email.split('@')[1],
      ip: req.ip,
    });

    // DEBUG: Console output in development only
    if (Var.node.env === 'dev') {
      console.log(`[DEV] Waitlist success: ${email.substring(0, 3)}***@${email.split('@')[1]}`);
    }

    // Return success response with minimal data
    return res.status(200).json({
      success: true,
      message: `${Var.app.emoji.success} Thank you! You've been added to our waitlist.`,
      payload: {
        email: result.payload?.email || email,
        addedAt: result.payload?.created_at || new Date().toISOString(),
      },
    });
  } catch (error: any) {
    // Handle unexpected errors
    logger.error('Waitlist controller error', {
      email: email.substring(0, 3) + '***@' + email.split('@')[1],
      error: error.message,
      stack: error.stack,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });

    // DEBUG: Console output in development only
    if (Var.node.env === 'dev') {
      console.error(`[DEV] Waitlist controller error:`, error);
    }

    return res.status(500).json({
      success: false,
      message: `${Var.app.emoji.failure} Something went wrong. Please try again later.`,
    });
  }
};
